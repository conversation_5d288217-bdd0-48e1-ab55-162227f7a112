using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.ModelsDTO.ShopSettings;
using Microsoft.EntityFrameworkCore;

namespace PosGTech.DataAccess.Repository
{
    /// <summary>
    /// مستودع إعدادات المتجر
    /// منفصل تماماً عن مستودع المخازن
    /// </summary>
    public class ShopSettingsRepository : Repository<ShopSettings>, IShopSettingsRepository
    {
        public ShopSettingsRepository(ApplicationDbContext db) : base(db)
        {
        }

        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        public void UpdateShopSettings(ShopSettings oldShopSettings, ShopSettingsDTO newShopSettings)
        {
            oldShopSettings.StoreName = newShopSettings.StoreName;
            oldShopSettings.StoreAddress = newShopSettings.StoreAddress;
            oldShopSettings.CompanyName = newShopSettings.CompanyName;
            oldShopSettings.CompanyPhone = newShopSettings.CompanyPhone;
            oldShopSettings.LogoPath = newShopSettings.LogoPath;
            oldShopSettings.IsDefault = newShopSettings.IsDefault;
            oldShopSettings.UpdatedAt = DateTime.Now;
            Update(oldShopSettings);
        }

        /// <summary>
        /// الحصول على الإعدادات الافتراضية للمتجر
        /// </summary>
        public async Task<ShopSettings?> GetDefaultShopSettingsAsync()
        {
            return await _db.Set<ShopSettings>()
                .FirstOrDefaultAsync(x => x.IsDefault);
        }

        /// <summary>
        /// تعيين إعدادات المتجر كافتراضية
        /// </summary>
        public async Task SetAsDefaultAsync(Guid shopSettingsId)
        {
            // إزالة الافتراضية من جميع الإعدادات الأخرى
            var allSettings = await _db.Set<ShopSettings>().ToListAsync();
            foreach (var setting in allSettings)
            {
                setting.IsDefault = setting.Id == shopSettingsId;
                setting.UpdatedAt = DateTime.Now;
            }
            
            _db.Set<ShopSettings>().UpdateRange(allSettings);
        }
    }
}
