@page "/sales-responsive-example"
@using PosGTech.Web.Pages.Components
@using PosGTech.ModelsDTO.Sells
@inject ISnackbar Snackbar

<PageTitle>مثال على استخدام الطباعة المتجاوبة</PageTitle>

<MudContainer MaxWidth="MaxWidth.ExtraLarge" Class="mt-4">
    <MudPaper Class="pa-4">
        <MudText Typo="Typo.h4" Class="mb-4">
            <MudIcon Icon="Icons.Material.Filled.Receipt" Class="me-2" />
            إدارة فواتير المبيعات - الطباعة المتجاوبة
        </MudText>

        <!-- جدول الفواتير -->
        <MudDataGrid T="SampleInvoice" 
                     Items="@sampleInvoices" 
                     Filterable="true" 
                     SortMode="SortMode.Multiple"
                     Class="mb-4">
            <Columns>
                <PropertyColumn Property="x => x.InvoiceNo" Title="رقم الفاتورة" />
                <PropertyColumn Property="x => x.Date" Title="التاريخ" Format="yyyy-MM-dd" />
                <PropertyColumn Property="x => x.ClientName" Title="العميل" />
                <PropertyColumn Property="x => x.Total" Title="الإجمالي" Format="C" />
                <PropertyColumn Property="x => x.FinalTotal" Title="الإجمالي النهائي" Format="C" />
                
                <TemplateColumn Title="الإجراءات" Sortable="false">
                    <CellTemplate>
                        <div class="d-flex gap-2 align-center">
                            <!-- استخدام مكون الطباعة المتجاوبة -->
                            <ResponsivePrintButton InvoiceId="@context.Item.Id"
                                                   OnPrintStarted="@((printType) => OnPrintStarted(context.Item, printType))"
                                                   OnPrintCompleted="@((printType) => OnPrintCompleted(context.Item, printType))"
                                                   OnPrintError="@((error) => OnPrintError(context.Item, error))" />
                            
                            <!-- أزرار إضافية -->
                            <MudIconButton Icon="Icons.Material.Filled.Edit" 
                                           Color="Color.Primary" 
                                           Size="Size.Small"
                                           Title="تعديل" />
                            <MudIconButton Icon="Icons.Material.Filled.Delete" 
                                           Color="Color.Error" 
                                           Size="Size.Small"
                                           Title="حذف" />
                        </div>
                    </CellTemplate>
                </TemplateColumn>
            </Columns>
        </MudDataGrid>

        <!-- إحصائيات الطباعة -->
        <MudCard Class="mt-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudText Typo="Typo.h6">إحصائيات الطباعة</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    <MudItem xs="12" sm="6" md="3">
                        <MudPaper Class="pa-4 text-center" Style="background: linear-gradient(45deg, #2196F3, #21CBF3);">
                            <MudText Typo="Typo.h4" Style="color: white;">@thermalPrintCount</MudText>
                            <MudText Typo="Typo.body1" Style="color: white;">طباعة حرارية</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudPaper Class="pa-4 text-center" Style="background: linear-gradient(45deg, #FF9800, #FFB74D);">
                            <MudText Typo="Typo.h4" Style="color: white;">@a4PrintCount</MudText>
                            <MudText Typo="Typo.body1" Style="color: white;">طباعة A4</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudPaper Class="pa-4 text-center" Style="background: linear-gradient(45deg, #4CAF50, #81C784);">
                            <MudText Typo="Typo.h4" Style="color: white;">@successfulPrints</MudText>
                            <MudText Typo="Typo.body1" Style="color: white;">طباعة ناجحة</MudText>
                        </MudPaper>
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudPaper Class="pa-4 text-center" Style="background: linear-gradient(45deg, #F44336, #EF5350);">
                            <MudText Typo="Typo.h4" Style="color: white;">@failedPrints</MudText>
                            <MudText Typo="Typo.body1" Style="color: white;">طباعة فاشلة</MudText>
                        </MudPaper>
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>

        <!-- سجل الطباعة -->
        <MudExpansionPanels Class="mt-4">
            <MudExpansionPanel Text="سجل الطباعة">
                <MudList T="string">
                    @foreach (var log in printLogs.OrderByDescending(x => x.Timestamp).Take(10))
                    {
                        <MudListItem T="string">
                            <div class="d-flex justify-space-between align-center">
                                <div>
                                    <MudText Typo="Typo.body1">
                                        فاتورة رقم @log.InvoiceNo - @log.ClientName
                                    </MudText>
                                    <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                        @log.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")
                                    </MudText>
                                </div>
                                <div class="d-flex align-center gap-2">
                                    <MudChip T="string" Color="@(log.PrintType == "Thermal" ? Color.Primary : Color.Secondary)"
                                             Size="Size.Small">
                                        @(log.PrintType == "Thermal" ? "حراري" : "A4")
                                    </MudChip>
                                    <MudChip T="string" Color="@(log.IsSuccess ? Color.Success : Color.Error)"
                                             Size="Size.Small">
                                        @(log.IsSuccess ? "نجح" : "فشل")
                                    </MudChip>
                                </div>
                            </div>
                        </MudListItem>
                        <MudDivider />
                    }
                </MudList>
            </MudExpansionPanel>
        </MudExpansionPanels>
    </MudPaper>
</MudContainer>

@code {
    // بيانات تجريبية للفواتير
    private List<SampleInvoice> sampleInvoices = new();
    
    // إحصائيات الطباعة
    private int thermalPrintCount = 0;
    private int a4PrintCount = 0;
    private int successfulPrints = 0;
    private int failedPrints = 0;
    
    // سجل الطباعة
    private List<PrintLog> printLogs = new();

    protected override void OnInitialized()
    {
        // إنشاء بيانات تجريبية
        sampleInvoices = new List<SampleInvoice>
        {
            new() { Id = Guid.NewGuid(), InvoiceNo = 1001, Date = DateOnly.FromDateTime(DateTime.Today), ClientName = "أحمد محمد", Total = 1500, FinalTotal = 1350 },
            new() { Id = Guid.NewGuid(), InvoiceNo = 1002, Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-1)), ClientName = "فاطمة علي", Total = 2200, FinalTotal = 2100 },
            new() { Id = Guid.NewGuid(), InvoiceNo = 1003, Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-2)), ClientName = "محمد حسن", Total = 800, FinalTotal = 750 },
            new() { Id = Guid.NewGuid(), InvoiceNo = 1004, Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-3)), ClientName = "سارة أحمد", Total = 3500, FinalTotal = 3200 },
            new() { Id = Guid.NewGuid(), InvoiceNo = 1005, Date = DateOnly.FromDateTime(DateTime.Today.AddDays(-4)), ClientName = "عبدالله محمود", Total = 1200, FinalTotal = 1200 }
        };
    }

    private void OnPrintStarted(SampleInvoice invoice, string printType)
    {
        Snackbar.Add($"بدء طباعة فاتورة رقم {invoice.InvoiceNo} - نوع الطباعة: {(printType == "Thermal" ? "حراري" : "A4")}", Severity.Info);
    }

    private void OnPrintCompleted(SampleInvoice invoice, string printType)
    {
        // تحديث الإحصائيات
        if (printType == "Thermal")
            thermalPrintCount++;
        else
            a4PrintCount++;
        
        successfulPrints++;

        // إضافة إلى السجل
        printLogs.Add(new PrintLog
        {
            InvoiceNo = invoice.InvoiceNo,
            ClientName = invoice.ClientName,
            PrintType = printType,
            IsSuccess = true,
            Timestamp = DateTime.Now
        });

        StateHasChanged();
        Snackbar.Add($"تمت طباعة فاتورة رقم {invoice.InvoiceNo} بنجاح", Severity.Success);
    }

    private void OnPrintError(SampleInvoice invoice, string error)
    {
        failedPrints++;
        
        // إضافة إلى السجل
        printLogs.Add(new PrintLog
        {
            InvoiceNo = invoice.InvoiceNo,
            ClientName = invoice.ClientName,
            PrintType = "Unknown",
            IsSuccess = false,
            Timestamp = DateTime.Now,
            ErrorMessage = error
        });

        StateHasChanged();
        Snackbar.Add($"فشل في طباعة فاتورة رقم {invoice.InvoiceNo}", Severity.Error);
    }

    // نماذج البيانات
    public class SampleInvoice
    {
        public Guid Id { get; set; }
        public int InvoiceNo { get; set; }
        public DateOnly Date { get; set; }
        public string ClientName { get; set; } = "";
        public decimal Total { get; set; }
        public decimal FinalTotal { get; set; }
    }

    public class PrintLog
    {
        public int InvoiceNo { get; set; }
        public string ClientName { get; set; } = "";
        public string PrintType { get; set; } = "";
        public bool IsSuccess { get; set; }
        public DateTime Timestamp { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
