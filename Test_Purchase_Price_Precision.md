# اختبار إصلاح دقة أسعار الشراء

## خطوات الاختبار

### الاختبار الأساسي - صنف جديد

#### 1. إنشاء صنف جديد:
```
- اسم الصنف: "اختبار دقة السعر"
- الوحدة الأساسية: "قطعة"
- بدون وحدات فرعية
```

#### 2. إنشاء فاتورة شراء:
```
- اختيار الصنف: "اختبار دقة السعر"
- الكمية: 10
- سعر الشراء: 1.75
- حفظ الفاتورة
```

#### 3. التحقق من النتيجة:
```sql
-- فحص قاعدة البيانات
SELECT Name, CostPrice FROM Items WHERE Name = 'اختبار دقة السعر';
-- النتيجة المتوقعة: CostPrice = 1.750
```

#### 4. اختبار فاتورة شراء ثانية:
```
- نفس الصنف
- الكمية: 5
- سعر الشراء: 2.25
- حفظ الفاتورة
```

#### 5. التحقق من النتيجة الثانية:
```sql
-- فحص قاعدة البيانات مرة أخرى
SELECT Name, CostPrice FROM Items WHERE Name = 'اختبار دقة السعر';
-- النتيجة المتوقعة: CostPrice = 2.250 (آخر سعر شراء)
```

### الاختبار المتقدم - وحدات متعددة

#### 1. إنشاء صنف بوحدات متعددة:
```
- اسم الصنف: "اختبار وحدات متعددة"
- الوحدة الأساسية: "كيلو" (كمية = 1)
- وحدة فرعية: "نصف كيلو" (كمية = 0.5، أصغر من الأساسية)
- وحدة فرعية: "ربع كيلو" (كمية = 0.25، أصغر من الأساسية)
```

#### 2. اختبار شراء بوحدة "نصف كيلو":
```
- اختيار الصنف: "اختبار وحدات متعددة"
- الوحدة: "نصف كيلو"
- الكمية: 20 (نصف كيلو)
- سعر الشراء: 1.75 (للنصف كيلو)
- حفظ الفاتورة
```

#### 3. التحقق من تحويل السعر:
```sql
-- فحص قاعدة البيانات
SELECT Name, CostPrice FROM Items WHERE Name = 'اختبار وحدات متعددة';
-- النتيجة المتوقعة: CostPrice = 3.500 (1.75 / 0.5 = 3.5 للكيلو الواحد)
```

#### 4. اختبار شراء بوحدة "ربع كيلو":
```
- نفس الصنف
- الوحدة: "ربع كيلو"
- الكمية: 40 (ربع كيلو)
- سعر الشراء: 1.25 (للربع كيلو)
- حفظ الفاتورة
```

#### 5. التحقق من النتيجة النهائية:
```sql
-- فحص قاعدة البيانات
SELECT Name, CostPrice FROM Items WHERE Name = 'اختبار وحدات متعددة';
-- النتيجة المتوقعة: CostPrice = 5.000 (1.25 / 0.25 = 5.0 للكيلو الواحد)
```

### اختبار الحالات الحدية

#### 1. أرقام عشرية معقدة:
```
- سعر الشراء: 1.333
- النتيجة المتوقعة: 1.333 (بدون تغيير)
```

#### 2. أرقام بمنازل عشرية كثيرة:
```
- سعر الشراء: 1.123456789
- النتيجة المتوقعة: 1.123 (تقريب إلى 3 منازل عشرية)
```

#### 3. تحويل وحدات معقد:
```
- وحدة بكمية: 3.333
- سعر الشراء: 10.00
- النتيجة المتوقعة: 3.000 (10.00 / 3.333 = 3.0003... ≈ 3.000)
```

## النتائج المتوقعة

### قبل الإصلاح:
- سعر 1.75 قد يصبح 1.703 أو قيمة مختلفة
- عدم ثبات في الأسعار المدخلة
- تأثر الأسعار بالمتوسط المرجح

### بعد الإصلاح:
- سعر 1.75 يبقى 1.750
- الاحتفاظ بآخر سعر شراء مدخل
- دقة في تحويل الوحدات

## كيفية تشغيل الاختبار

### 1. من خلال واجهة المستخدم:
1. تشغيل التطبيق
2. الذهاب إلى صفحة الأصناف
3. إنشاء الأصناف المطلوبة للاختبار
4. الذهاب إلى صفحة فواتير الشراء
5. إنشاء الفواتير حسب السيناريوهات أعلاه
6. التحقق من النتائج في صفحة الأصناف أو قاعدة البيانات

### 2. من خلال قاعدة البيانات:
```sql
-- فحص جميع الأصناف وأسعارها
SELECT 
    i.Name,
    i.CostPrice,
    pi.Price as LastPurchasePrice,
    pi.CreatedAt
FROM Items i
LEFT JOIN PurchaseItems pi ON i.Id = pi.ItemId
ORDER BY i.Name, pi.CreatedAt DESC;
```

### 3. من خلال API (إذا متوفر):
```http
GET /api/items/{itemId}
```

## معايير النجاح

✅ **الاختبار ناجح إذا**:
- أسعار الشراء المدخلة تحفظ بنفس القيمة (مع 3 منازل عشرية)
- تحويل الوحدات يتم بدقة صحيحة
- آخر سعر شراء يظهر في `Item.CostPrice`
- عدم وجود أرقام عشرية غريبة (مثل 1.703 بدلاً من 1.750)

❌ **الاختبار فاشل إذا**:
- أسعار الشراء تتغير عن القيم المدخلة
- ظهور أرقام عشرية غير متوقعة
- عدم دقة في تحويل الوحدات
- استمرار استخدام المتوسط المرجح بدلاً من آخر سعر

## ملاحظات الاختبار

1. **تأكد من تطبيق Migration**: قاعدة البيانات يجب أن تدعم 3 منازل عشرية
2. **مسح البيانات القديمة**: للحصول على نتائج دقيقة، قم بمسح البيانات التجريبية القديمة
3. **اختبار متصفحات مختلفة**: تأكد من أن المشكلة محلولة في جميع المتصفحات
4. **اختبار أجهزة مختلفة**: تأكد من عدم تأثر النتائج بنوع الجهاز أو نظام التشغيل

## تقرير الاختبار

بعد تشغيل الاختبارات، قم بتوثيق:

### النتائج:
- [ ] الاختبار الأساسي: ناجح/فاشل
- [ ] اختبار الوحدات المتعددة: ناجح/فاشل  
- [ ] اختبار الحالات الحدية: ناجح/فاشل

### المشاكل المكتشفة:
- قائمة بأي مشاكل أو سلوكيات غير متوقعة

### التوصيات:
- أي تحسينات إضافية مطلوبة
