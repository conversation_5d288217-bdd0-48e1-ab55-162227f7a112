using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Employees;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للموظفين - يحتوي على جميع العمليات المطلوبة للتعامل مع الموظفين
    /// </summary>
    public class EmployeeApiRepository : IEmployeeApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<EmployeeApiRepository> _logger;

        public EmployeeApiRepository(HttpClient httpClient, ILogger<EmployeeApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الموظفين
        /// </summary>
        public async Task<(IEnumerable<EmployeeDTO>? list, ResponseVM? response)> GetAllEmployeesAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع الموظفين");

                var httpResponse = await _httpClient.GetAsync("Employees/getAllEmployees");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var employees = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<EmployeeDTO>>();
                    _logger.LogInformation("تم جلب {Count} موظف بنجاح", employees?.Count() ?? 0);
                    return (employees, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الموظفين: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الموظفين: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الموظفين");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب جميع الموظفين للقائمة المنسدلة
        /// </summary>
        public async Task<(IEnumerable<EmployeeCMDTO>? list, ResponseVM? response)> GetAllEmployeesCMAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع الموظفين للقائمة المنسدلة");

                var httpResponse = await _httpClient.GetAsync("Employees/getAllEmployeesCM");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var employees = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<EmployeeCMDTO>>();
                    _logger.LogInformation("تم جلب {Count} موظف للقائمة المنسدلة بنجاح", employees?.Count() ?? 0);
                    return (employees, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الموظفين للقائمة المنسدلة: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الموظفين: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الموظفين للقائمة المنسدلة");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب موظف بالمعرف
        /// </summary>
        public async Task<(EmployeeDTO? model, ResponseVM? response)> GetEmployeeByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب الموظف بالمعرف: {EmployeeId}", id);

                var httpResponse = await _httpClient.GetAsync($"Employees/getEmployeeById/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var employee = await httpResponse.Content.ReadFromJsonAsync<EmployeeDTO>();
                    _logger.LogInformation("تم جلب الموظف بالمعرف {EmployeeId} بنجاح", id);
                    return (employee, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الموظف بالمعرف {EmployeeId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الموظف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الموظف بالمعرف: {EmployeeId}", id);
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// إضافة موظف جديد
        /// </summary>
        public async Task<ResponseVM> InsertEmployeeAsync(EmployeeDTO employee)
        {
            try
            {
                _logger.LogInformation("بدء عملية إضافة موظف جديد: {EmployeeName}", employee.Name);

                var json = JsonSerializer.Serialize(employee);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PostAsync("Employees/insertEmployee", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم إضافة الموظف {EmployeeName} بنجاح", employee.Name);
                    return response ?? new ResponseVM { Message = "تم إضافة الموظف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في إضافة الموظف {EmployeeName}: {StatusCode} - {Error}", employee.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في إضافة الموظف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة الموظف: {EmployeeName}", employee.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// تحديث موظف موجود
        /// </summary>
        public async Task<ResponseVM> UpdateEmployeeAsync(Guid id, EmployeeDTO employee)
        {
            try
            {
                _logger.LogInformation("بدء عملية تحديث الموظف: {EmployeeId} - {EmployeeName}", id, employee.Name);

                var json = JsonSerializer.Serialize(employee);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PutAsync($"Employees/updateEmployee/{id}", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم تحديث الموظف {EmployeeId} - {EmployeeName} بنجاح", id, employee.Name);
                    return response ?? new ResponseVM { Message = "تم تحديث الموظف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في تحديث الموظف {EmployeeId} - {EmployeeName}: {StatusCode} - {Error}", id, employee.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في تحديث الموظف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث الموظف: {EmployeeId} - {EmployeeName}", id, employee.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// حذف موظف
        /// </summary>
        public async Task<ResponseVM> DeleteEmployeeAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية حذف الموظف: {EmployeeId}", id);

                var httpResponse = await _httpClient.DeleteAsync($"Employees/deleteEmployee/{id}");
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم حذف الموظف {EmployeeId} بنجاح", id);
                    return response ?? new ResponseVM { Message = "تم حذف الموظف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في حذف الموظف {EmployeeId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في حذف الموظف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء حذف الموظف: {EmployeeId}", id);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }
    }
}
