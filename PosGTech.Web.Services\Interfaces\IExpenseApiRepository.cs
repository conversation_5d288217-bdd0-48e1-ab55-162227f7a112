using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Expenses;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// واجهة مستودع API للمصروفات - تحتوي على جميع العمليات المطلوبة للتعامل مع المصروفات
    /// </summary>
    public interface IExpenseApiRepository
    {
        /// <summary>
        /// جلب جميع المصروفات
        /// </summary>
        /// <returns>قائمة المصروفات مع حالة الاستجابة</returns>
        Task<(IEnumerable<ExpenseDTO>? list, ResponseVM? response)> GetAllExpensesAsync();

        /// <summary>
        /// جلب مصروف بالمعرف
        /// </summary>
        /// <param name="id">معرف المصروف</param>
        /// <returns>بيانات المصروف مع حالة الاستجابة</returns>
        Task<(ExpenseDTO? model, ResponseVM? response)> GetExpenseByIdAsync(Guid id);

        /// <summary>
        /// إضافة مصروف جديد
        /// </summary>
        /// <param name="expense">بيانات المصروف الجديد</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> InsertExpenseAsync(ExpenseDTO expense);

        /// <summary>
        /// تحديث مصروف موجود
        /// </summary>
        /// <param name="id">معرف المصروف</param>
        /// <param name="expense">بيانات المصروف المحدثة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> UpdateExpenseAsync(Guid id, ExpenseDTO expense);

        /// <summary>
        /// حذف مصروف
        /// </summary>
        /// <param name="id">معرف المصروف</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> DeleteExpenseAsync(Guid id);
    }
}
