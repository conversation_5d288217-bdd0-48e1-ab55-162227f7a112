using Microsoft.AspNetCore.Authorization;

namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// متطلب التفويض المبني على الصلاحيات
/// يحدد الصلاحية المطلوبة للوصول إلى مورد معين
/// </summary>
public class PermissionRequirement : IAuthorizationRequirement
{
    /// <summary>
    /// الصلاحية المطلوبة
    /// </summary>
    public string Permission { get; }

    /// <summary>
    /// منشئ متطلب الصلاحية
    /// </summary>
    /// <param name="permission">الصلاحية المطلوبة</param>
    public PermissionRequirement(string permission)
    {
        Permission = permission ?? throw new ArgumentNullException(nameof(permission));
    }
}
