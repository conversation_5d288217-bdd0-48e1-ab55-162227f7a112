using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.StoreItem;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API لعناصر المخزن - يحتوي على جميع العمليات المطلوبة للتعامل مع عناصر المخزن
    /// </summary>
    public class StoreItemApiRepository : IStoreItemApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<StoreItemApiRepository> _logger;

        public StoreItemApiRepository(HttpClient httpClient, ILogger<StoreItemApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع عناصر المخزن
        /// </summary>
        public async Task<(IEnumerable<StoreItemDTO>? list, ResponseVM? response)> GetAllStoreItemsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع عناصر المخزن");
                var response = await _httpClient.GetAsync("StoreItems/getAllStoreItems");

                if (response.IsSuccessStatusCode)
                {
                    var storeItems = await response.Content.ReadFromJsonAsync<StoreItemDTO[]>();
                    return (storeItems, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عناصر المخزن");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب عناصر المخزن للبيع
        /// </summary>
        public async Task<(IEnumerable<StoreItemForSellDTO>? list, ResponseVM? response)> GetStoreItemsForSellAsync()
        {
            try
            {
                _logger.LogInformation("جلب عناصر المخزن للبيع");
                var response = await _httpClient.GetAsync("StoreItems/getStoreItemsForSell");

                if (response.IsSuccessStatusCode)
                {
                    var storeItems = await response.Content.ReadFromJsonAsync<StoreItemForSellDTO[]>();
                    return (storeItems, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عناصر المخزن للبيع");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب عناصر المخزن للبيع حسب المخزن
        /// </summary>
        public async Task<(IEnumerable<StoreItemDTO>? list, ResponseVM? response)> GetStoreItemsForSellAsync(Guid storeId)
        {
            try
            {
                _logger.LogInformation("جلب عناصر المخزن للبيع حسب المخزن: {StoreId}", storeId);
                var response = await _httpClient.GetAsync($"StoreItems/getStoreItemsForSell/{storeId}");

                if (response.IsSuccessStatusCode)
                {
                    var storeItems = await response.Content.ReadFromJsonAsync<StoreItemDTO[]>();
                    return (storeItems, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عناصر المخزن للبيع حسب المخزن: {StoreId}", storeId);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب عنصر مخزن بالمعرف
        /// </summary>
        public async Task<(StoreItemDTO? model, ResponseVM? response)> GetStoreItemByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب عنصر المخزن بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"StoreItems/getStoreItemById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var storeItem = await response.Content.ReadFromJsonAsync<StoreItemDTO>();
                    return (storeItem, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عنصر المخزن بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة عنصر مخزن جديد
        /// </summary>
        public async Task<ResponseVM> InsertStoreItemAsync(StoreItemDTO storeItem)
        {
            try
            {
                _logger.LogInformation("إضافة عنصر مخزن جديد");
                var response = await _httpClient.PostAsJsonAsync("StoreItems/insertStoreItem", storeItem);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة عنصر المخزن بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة عنصر المخزن");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث عنصر مخزن موجود
        /// </summary>
        public async Task<ResponseVM> UpdateStoreItemAsync(Guid id, StoreItemDTO storeItem)
        {
            try
            {
                _logger.LogInformation("تحديث عنصر المخزن: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"StoreItems/updateStoreItem/{id}", storeItem);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث عنصر المخزن بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عنصر المخزن: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف عنصر مخزن
        /// </summary>
        public async Task<ResponseVM> DeleteStoreItemAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف عنصر المخزن: {Id}", id);
                var response = await _httpClient.DeleteAsync($"StoreItems/deleteStoreItem/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف عنصر المخزن بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف عنصر المخزن: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}
