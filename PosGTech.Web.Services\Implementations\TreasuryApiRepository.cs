using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Treasury;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للخزائن - يحتوي على جميع العمليات المطلوبة للتعامل مع الخزائن
    /// </summary>
    public class TreasuryApiRepository : ITreasuryApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<TreasuryApiRepository> _logger;

        public TreasuryApiRepository(HttpClient httpClient, ILogger<TreasuryApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الخزائن
        /// </summary>
        public async Task<(IEnumerable<TreasuryDTO>? list, ResponseVM? response)> GetAllTreasuriesAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الخزائن");
                var response = await _httpClient.GetAsync("Treasuries/getAllTreasuries");

                if (response.IsSuccessStatusCode)
                {
                    var treasuries = await response.Content.ReadFromJsonAsync<TreasuryDTO[]>();
                    return (treasuries, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الخزائن");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع الخزائن للكومبو بوكس
        /// </summary>
        public async Task<(IEnumerable<TreasuryCMDTO>? list, ResponseVM? response)> GetAllTreasuriesCMBAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الخزائن للكومبو بوكس");
                var response = await _httpClient.GetAsync("Treasuries/getAllTreasuriesCMB");

                if (response.IsSuccessStatusCode)
                {
                    var treasuries = await response.Content.ReadFromJsonAsync<TreasuryCMDTO[]>();
                    return (treasuries, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الخزائن للكومبو بوكس");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب خزينة بالمعرف
        /// </summary>
        public async Task<(TreasuryDTO? model, ResponseVM? response)> GetTreasuryByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب الخزينة بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Treasuries/getTreasuryById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var treasury = await response.Content.ReadFromJsonAsync<TreasuryDTO>();
                    return (treasury, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الخزينة بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة خزينة جديدة
        /// </summary>
        public async Task<ResponseVM> InsertTreasuryAsync(TreasuryDTO treasury)
        {
            try
            {
                _logger.LogInformation("إضافة خزينة جديدة: {Name}", treasury.Name);
                var response = await _httpClient.PostAsJsonAsync("Treasuries/insertTreasury", treasury);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة الخزينة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الخزينة: {Name}", treasury.Name);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث خزينة موجودة
        /// </summary>
        public async Task<ResponseVM> UpdateTreasuryAsync(Guid id, TreasuryDTO treasury)
        {
            try
            {
                _logger.LogInformation("تحديث الخزينة: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Treasuries/updateTreasury/{id}", treasury);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث الخزينة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الخزينة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف خزينة
        /// </summary>
        public async Task<ResponseVM> DeleteTreasuryAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف الخزينة: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Treasuries/deleteTreasury/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف الخزينة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الخزينة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب خزائن المستخدم للكومبو بوكس
        /// </summary>
        public async Task<(IEnumerable<UserTreasuryCMDTO>? list, ResponseVM? response)> GetUserTreasuriesCMBAsync()
        {
            try
            {
                _logger.LogInformation("جلب خزائن المستخدم للكومبو بوكس");
                var response = await _httpClient.GetAsync("Treasuries/getUserTreasuriesCMB");

                if (response.IsSuccessStatusCode)
                {
                    var userTreasuries = await response.Content.ReadFromJsonAsync<UserTreasuryCMDTO[]>();
                    return (userTreasuries, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب خزائن المستخدم للكومبو بوكس");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}
