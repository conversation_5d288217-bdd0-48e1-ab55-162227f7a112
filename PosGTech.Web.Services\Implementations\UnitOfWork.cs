using Microsoft.Extensions.Logging;
using PosGTech.Web.Services.Interfaces;
using Blazored.LocalStorage;
using System.Net.Http.Headers;
using System.Text.Json;
using System.IdentityModel.Tokens.Jwt;

namespace PosGTech.Web.Services.Implementations
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly HttpClient _httpClient;
        private readonly ILocalStorageService _localStorage;
        private readonly ILogger<UnitOfWork> _logger;
        private string? _currentToken;
        private DateTime _tokenExpiry = DateTime.MinValue;

        // الوحدات الموجودة حالياً
        public ICategoryApiRepository Category { get; private set; }
        public IClientApiRepository Client { get; private set; }
        public IStoreApiRepository Store { get; private set; }
        public IItemApiRepository Item { get; private set; }
        public IExpenseApiRepository Expense { get; private set; }
        public IEmployeeApiRepository Employee { get; private set; }

        // الوحدات الأساسية الجديدة
        public IPurchaseApiRepository Purchase { get; private set; }
        public ISellApiRepository Sell { get; private set; }
        public IReceiptApiRepository Receipt { get; private set; }
        public IFinancialApiRepository Financial { get; private set; }
        public ITreasuryApiRepository Treasury { get; private set; }
        public IUnitApiRepository Unit { get; private set; }

        // الوحدات المتقدمة الجديدة
        public IInventoryApiRepository Inventory { get; private set; }
        public IConsumedApiRepository Consumed { get; private set; }
        public IStoreItemApiRepository StoreItem { get; private set; }

        // وحدات الإدارة الجديدة
        public IUserApiRepository User { get; private set; }
        public IRoleApiRepository Role { get; private set; }
        public IShopSettingsApiRepository ShopSettings { get; private set; }

        public UnitOfWork(HttpClient httpClient, ILoggerFactory loggerFactory, ILocalStorageService localStorage)
        {
            _httpClient = httpClient;
            _localStorage = localStorage;
            _logger = loggerFactory.CreateLogger<UnitOfWork>();

            // إعداد التوكن تلقائياً عند إنشاء UnitOfWork
            _ = Task.Run(async () => await CheckAndSetTokenAsync());

            // تهيئة الوحدات الموجودة حالياً
            Category = new CategoryApiRepository(httpClient, loggerFactory.CreateLogger<CategoryApiRepository>());
            Client = new ClientApiRepository(httpClient, loggerFactory.CreateLogger<ClientApiRepository>());
            Store = new StoreApiRepository(httpClient, loggerFactory.CreateLogger<StoreApiRepository>());
            Item = new ItemApiRepository(httpClient, loggerFactory.CreateLogger<ItemApiRepository>());
            Expense = new ExpenseApiRepository(httpClient, loggerFactory.CreateLogger<ExpenseApiRepository>());
            Employee = new EmployeeApiRepository(httpClient, loggerFactory.CreateLogger<EmployeeApiRepository>());

            // تهيئة الوحدات الأساسية الجديدة
            Purchase = new PurchaseApiRepository(httpClient, loggerFactory.CreateLogger<PurchaseApiRepository>());
            Sell = new SellApiRepository(httpClient, loggerFactory.CreateLogger<SellApiRepository>());
            Receipt = new ReceiptApiRepository(httpClient, loggerFactory.CreateLogger<ReceiptApiRepository>());
            Financial = new FinancialApiRepository(httpClient, loggerFactory.CreateLogger<FinancialApiRepository>());
            Treasury = new TreasuryApiRepository(httpClient, loggerFactory.CreateLogger<TreasuryApiRepository>());
            Unit = new UnitApiRepository(httpClient, loggerFactory.CreateLogger<UnitApiRepository>());

            // تهيئة الوحدات المتقدمة الجديدة
            Inventory = new InventoryApiRepository(httpClient, loggerFactory.CreateLogger<InventoryApiRepository>());
            Consumed = new ConsumedApiRepository(httpClient, loggerFactory.CreateLogger<ConsumedApiRepository>());
            StoreItem = new StoreItemApiRepository(httpClient, loggerFactory.CreateLogger<StoreItemApiRepository>());

            // تهيئة وحدات الإدارة الجديدة
            User = new UserApiRepository(httpClient, loggerFactory.CreateLogger<UserApiRepository>());
            Role = new RoleApiRepository(httpClient, loggerFactory.CreateLogger<RoleApiRepository>());
            ShopSettings = new ShopSettingsApiRepository(httpClient, loggerFactory.CreateLogger<ShopSettingsApiRepository>());
        }

        /// <summary>
        /// فحص وإعداد التوكن من LocalStorage
        /// </summary>
        public async Task CheckAndSetTokenAsync()
        {
            try
            {
                var token = await _localStorage.GetItemAsync<string>("authToken");
                if (!string.IsNullOrEmpty(token))
                {
                    await SetAuthorizationHeaderAsync(token);
                    _logger.LogInformation("تم إعداد التوكن بنجاح في HttpClient");
                }
                else
                {
                    _logger.LogWarning("لم يتم العثور على التوكن في LocalStorage");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص وإعداد التوكن");
            }
        }

        /// <summary>
        /// إعداد Authorization Header مع JWT Token
        /// </summary>
        private async Task SetAuthorizationHeaderAsync(string token)
        {
            try
            {
                // التحقق من صحة التوكن وانتهاء صلاحيته
                if (IsTokenValid(token))
                {
                    _currentToken = token;
                    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
                    _logger.LogInformation("تم إعداد Authorization Header بنجاح");
                }
                else
                {
                    _logger.LogWarning("التوكن غير صالح أو منتهي الصلاحية");
                    await ClearTokenAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعداد Authorization Header");
            }
        }

        /// <summary>
        /// التحقق من صحة التوكن وانتهاء صلاحيته
        /// </summary>
        private bool IsTokenValid(string token)
        {
            try
            {
                var jwtHandler = new JwtSecurityTokenHandler();
                if (!jwtHandler.CanReadToken(token))
                {
                    return false;
                }

                var jwtToken = jwtHandler.ReadJwtToken(token);
                _tokenExpiry = jwtToken.ValidTo;

                // التحقق من انتهاء الصلاحية مع هامش أمان 5 دقائق
                return _tokenExpiry > DateTime.UtcNow.AddMinutes(5);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحقق من صحة التوكن");
                return false;
            }
        }

        /// <summary>
        /// تجديد التوكن إذا كان قريب من انتهاء الصلاحية
        /// </summary>
        public async Task<bool> RefreshTokenIfNeededAsync()
        {
            try
            {
                // التحقق من الحاجة لتجديد التوكن (إذا كان سينتهي خلال 10 دقائق)
                if (_tokenExpiry <= DateTime.UtcNow.AddMinutes(10))
                {
                    _logger.LogInformation("محاولة تجديد التوكن");

                    // محاولة الحصول على التوكن الجديد من LocalStorage
                    var newToken = await _localStorage.GetItemAsync<string>("authToken");
                    if (!string.IsNullOrEmpty(newToken) && newToken != _currentToken)
                    {
                        await SetAuthorizationHeaderAsync(newToken);
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning("فشل في تجديد التوكن");
                        return false;
                    }
                }
                return true; // التوكن مازال صالح
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تجديد التوكن");
                return false;
            }
        }

        /// <summary>
        /// مسح التوكن من HttpClient وLocalStorage
        /// </summary>
        public async Task ClearTokenAsync()
        {
            try
            {
                _currentToken = null;
                _tokenExpiry = DateTime.MinValue;
                _httpClient.DefaultRequestHeaders.Authorization = null;
                await _localStorage.RemoveItemAsync("authToken");
                _logger.LogInformation("تم مسح التوكن بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مسح التوكن");
            }
        }

        /// <summary>
        /// معالجة استجابة 401 Unauthorized
        /// </summary>
        public async Task<bool> HandleUnauthorizedResponseAsync()
        {
            try
            {
                _logger.LogWarning("تم استلام استجابة 401 Unauthorized - محاولة تجديد التوكن");

                // محاولة تجديد التوكن
                if (await RefreshTokenIfNeededAsync())
                {
                    return true; // تم تجديد التوكن بنجاح
                }
                else
                {
                    // فشل في تجديد التوكن - مسح التوكن وإعادة توجيه للتسجيل
                    await ClearTokenAsync();
                    _logger.LogError("فشل في تجديد التوكن - يجب إعادة تسجيل الدخول");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في معالجة استجابة 401");
                return false;
            }
        }

        public void Dispose()
        {
        }
    }
}
