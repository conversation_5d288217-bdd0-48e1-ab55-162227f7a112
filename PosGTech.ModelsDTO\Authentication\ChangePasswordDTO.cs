using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Authentication
{
    /// <summary>
    /// نموذج بيانات تغيير كلمة المرور
    /// </summary>
    public class ChangePasswordDTO
    {
        /// <summary>
        /// كلمة المرور الحالية
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور الحالية مطلوبة")]
        [DataType(DataType.Password)]
        public string CurrentPassword { get; set; }

        /// <summary>
        /// كلمة المرور الجديدة
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور الجديدة مطلوبة")]
        [StringLength(100, ErrorMessage = "يجب أن تحتوي على حروف ورموز وأرقام", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string NewPassword { get; set; }

        /// <summary>
        /// تأكيد كلمة المرور الجديدة
        /// </summary>
        [Required(ErrorMessage = "تأكيد كلمة المرور مطلوب")]
        [DataType(DataType.Password)]
        [Compare("NewPassword", ErrorMessage = "كلمة المرور غير مطابقة")]
        public string ConfirmNewPassword { get; set; }
    }
}
