namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// فئة تحتوي على تجميع الصلاحيات حسب الوحدات الوظيفية
/// يساعد في تنظيم الصلاحيات وتسهيل إدارتها
/// </summary>
public static class PermissionGroups
{
    /// <summary>
    /// تجميع الصلاحيات حسب الوحدات الوظيفية
    /// </summary>
    public static readonly Dictionary<string, List<string>> Groups = new()
    {
        {
            "إدارة الأصناف والتصنيفات",
            new List<string>
            {
                PermissionConstants.ItemsView,
                PermissionConstants.ItemsAdd,
                PermissionConstants.ItemsEdit,
                PermissionConstants.ItemsDelete,
                PermissionConstants.CategoriesView,
                PermissionConstants.CategoriesAdd,
                PermissionConstants.CategoriesEdit,
                PermissionConstants.CategoriesDelete,
                PermissionConstants.UnitsView,
                PermissionConstants.UnitsAdd,
                PermissionConstants.UnitsEdit,
                PermissionConstants.UnitsDelete
            }
        },
        {
            "إدارة المبيعات",
            new List<string>
            {
                PermissionConstants.SalesView,
                PermissionConstants.SalesAdd,
                PermissionConstants.SalesEdit,
                PermissionConstants.SalesDelete,
                PermissionConstants.ClientsView,
                PermissionConstants.ClientsAdd,
                PermissionConstants.ClientsEdit
            }
        },
        {
            "إدارة المشتريات",
            new List<string>
            {
                PermissionConstants.PurchasesView,
                PermissionConstants.PurchasesAdd,
                PermissionConstants.PurchasesEdit,
                PermissionConstants.PurchasesDelete,
                PermissionConstants.ClientsView,
                PermissionConstants.ClientsAdd,
                PermissionConstants.ClientsEdit
            }
        },
        {
            "إدارة المخزون",
            new List<string>
            {
                PermissionConstants.InventoryView,
                PermissionConstants.InventoryAdd,
                PermissionConstants.InventoryEdit,
                PermissionConstants.InventoryDelete,
                PermissionConstants.StoresView,
                PermissionConstants.StoresAdd,
                PermissionConstants.StoresEdit,
                PermissionConstants.StoresDelete,
                PermissionConstants.ItemsView
            }
        },
        {
            "الإدارة المالية",
            new List<string>
            {
                PermissionConstants.TreasuriesView,
                PermissionConstants.TreasuriesAdd,
                PermissionConstants.TreasuriesEdit,
                PermissionConstants.TreasuriesDelete,
                PermissionConstants.ReceiptsView,
                PermissionConstants.ReceiptsAdd,
                PermissionConstants.ReceiptsEdit,
                PermissionConstants.ReceiptsDelete,
                PermissionConstants.ExpensesView,
                PermissionConstants.ExpensesAdd,
                PermissionConstants.ExpensesEdit,
                PermissionConstants.ExpensesDelete
            }
        },
        {
            "إدارة الموارد البشرية",
            new List<string>
            {
                PermissionConstants.EmployeesView,
                PermissionConstants.EmployeesAdd,
                PermissionConstants.EmployeesEdit,
                PermissionConstants.EmployeesDelete,
                PermissionConstants.UsersView,
                PermissionConstants.UsersAdd,
                PermissionConstants.UsersEdit,
                PermissionConstants.UsersDelete
            }
        },
        {
            "التقارير والإحصائيات",
            new List<string>
            {
                PermissionConstants.ReportsView,
                PermissionConstants.ReportsGenerate,
                PermissionConstants.ReportsExport,
                PermissionConstants.ReportsAdvanced
            }
        },
        {
            "إدارة النظام",
            new List<string>
            {
                PermissionConstants.RolesView,
                PermissionConstants.RolesAdd,
                PermissionConstants.RolesEdit,
                PermissionConstants.RolesDelete,
                PermissionConstants.SystemSettings,
                PermissionConstants.SystemBackup,
                PermissionConstants.SystemRestore,
                PermissionConstants.SystemLogs
            }
        }
    };

    /// <summary>
    /// الحصول على صلاحيات مجموعة معينة
    /// </summary>
    /// <param name="groupName">اسم المجموعة</param>
    /// <returns>قائمة بصلاحيات المجموعة</returns>
    public static List<string> GetGroupPermissions(string groupName)
    {
        return Groups.TryGetValue(groupName, out var permissions) ? permissions : new List<string>();
    }

    /// <summary>
    /// الحصول على جميع أسماء المجموعات
    /// </summary>
    /// <returns>قائمة بأسماء المجموعات</returns>
    public static List<string> GetGroupNames()
    {
        return Groups.Keys.ToList();
    }

    /// <summary>
    /// البحث عن المجموعة التي تحتوي على صلاحية معينة
    /// </summary>
    /// <param name="permission">الصلاحية</param>
    /// <returns>اسم المجموعة أو null إذا لم توجد</returns>
    public static string? FindGroupForPermission(string permission)
    {
        return Groups.FirstOrDefault(g => g.Value.Contains(permission)).Key;
    }

    /// <summary>
    /// الحصول على صلاحيات أساسية للمبتدئين
    /// </summary>
    /// <returns>قائمة بالصلاحيات الأساسية</returns>
    public static List<string> GetBasicPermissions()
    {
        return new List<string>
        {
            PermissionConstants.ItemsView,
            PermissionConstants.CategoriesView,
            PermissionConstants.UnitsView,
            PermissionConstants.ClientsView,
            PermissionConstants.StoresView,
            PermissionConstants.ReportsView
        };
    }

    /// <summary>
    /// الحصول على صلاحيات المبيعات الأساسية
    /// </summary>
    /// <returns>قائمة بصلاحيات المبيعات</returns>
    public static List<string> GetSalesPermissions()
    {
        return new List<string>
        {
            PermissionConstants.SalesView,
            PermissionConstants.SalesAdd,
            PermissionConstants.SalesEdit,
            PermissionConstants.ClientsView,
            PermissionConstants.ClientsAdd,
            PermissionConstants.ClientsEdit,
            PermissionConstants.ItemsView,
            PermissionConstants.TreasuriesView,
            PermissionConstants.ReportsView
        };
    }

    /// <summary>
    /// الحصول على صلاحيات المشتريات الأساسية
    /// </summary>
    /// <returns>قائمة بصلاحيات المشتريات</returns>
    public static List<string> GetPurchasePermissions()
    {
        return new List<string>
        {
            PermissionConstants.PurchasesView,
            PermissionConstants.PurchasesAdd,
            PermissionConstants.PurchasesEdit,
            PermissionConstants.PurchasesDelete,
            PermissionConstants.ClientsView,
            PermissionConstants.ClientsAdd,
            PermissionConstants.ClientsEdit,
            PermissionConstants.InventoryView,
            PermissionConstants.InventoryAdd,
            PermissionConstants.InventoryEdit,
            PermissionConstants.ItemsView,
            PermissionConstants.ItemsAdd,
            PermissionConstants.ItemsEdit,
            PermissionConstants.StoresView,
            PermissionConstants.ReportsView
        };
    }
}
