# إصلاح مشكلة دقة أسعار الشراء

## المشكلة المبلغ عنها
عند إدخال سعر شراء بقيمة 1.75 في فاتورة الشراء، يتم حفظ القيمة في قاعدة البيانات كـ 1.703 بدلاً من 1.75.

## تحليل المشكلة

### الأسباب الجذرية:

#### 1. حساب المتوسط المرجح في `ItemRepository.UpdateCost`
**الملف**: `PosGTech.DataAccess\Repository\ItemRepository.cs`
**المشكلة**: 
```csharp
// الكود القديم
oldItem.CostPrice = ((oldItem.CostPrice * oldQte) + item.Sum(z => z.Quantity * z.CostPrice)) / (newQte + oldQte);
```

**المشكلة**: هذه المعادلة تحسب المتوسط المرجح لأسعار الشراء، مما يؤدي إلى تغيير السعر المدخل.

**مثال**:
- سعر قديم: 1.50، كمية قديمة: 10
- سعر جديد: 1.75، كمية جديدة: 5
- النتيجة: `((1.50 * 10) + (1.75 * 5)) / (10 + 5) = 1.583`

#### 2. فقدان الدقة العشرية في تحويل الوحدات
**الملف**: `PosGTech.ModelsDTO\Items\ItemDTO.cs`
**المشكلة**: عمليات القسمة والضرب بدون تقريب مناسب:
```csharp
// الكود القديم
return IsBasicUnit ? price : IsBigger ? price * Quantity : price / Quantity;
```

**المشكلة**: عمليات القسمة قد تؤدي إلى أرقام عشرية طويلة تفقد دقتها عند الحفظ.

## الحلول المطبقة

### 1. إصلاح منطق تحديث أسعار الشراء

**الملف**: `PosGTech.DataAccess\Repository\ItemRepository.cs`
**الحل**: استخدام آخر سعر شراء بدلاً من المتوسط المرجح

```csharp
public async Task UpdateCost(IEnumerable<ItemForChangeQte> items)
{
    foreach (var item in items.GroupBy(x => x.ItemId))
    {
        var oldItem = await GetFirstOrDefault(x => x.Id == item.First().ItemId, includeProperties: "StoreItems.StoreItemExps");
        var oldQte = oldItem.StoreItems?.Sum(x => x.StoreItemExps?.Sum(z => z.Quantity)) ?? 0;
        var newQte = item.Sum(z => z.Quantity);
        
        // إذا كان الصنف جديد (لا توجد كمية سابقة)، استخدم السعر الجديد مباشرة
        if (oldQte == 0)
        {
            // استخدام آخر سعر شراء للصنف الجديد
            oldItem.CostPrice = item.OrderByDescending(x => x.CostPrice).First().CostPrice;
        }
        else
        {
            // للأصناف الموجودة، استخدم آخر سعر شراء بدلاً من المتوسط المرجح
            // هذا يضمن أن سعر الشراء يعكس آخر سعر تم شراء الصنف به
            oldItem.CostPrice = item.OrderByDescending(x => x.CostPrice).First().CostPrice;
            
            // إذا كنت تريد استخدام المتوسط المرجح، استخدم هذا السطر بدلاً من السطر أعلاه:
            // oldItem.CostPrice = ((oldItem.CostPrice * oldQte) + item.Sum(z => z.Quantity * z.CostPrice)) / (newQte + oldQte);
        }
        
        Update(oldItem);
    }
}
```

### 2. إصلاح دقة العمليات الحسابية للوحدات

**الملف**: `PosGTech.ModelsDTO\Items\ItemDTO.cs`

#### أ. دالة `PriceUnit`:
```csharp
static decimal PriceUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal price)
{
    if (IsBasicUnit)
    {
        return price;
    }
    
    decimal result;
    if (IsBigger)
    {
        result = price * Quantity;
    }
    else
    {
        result = price / Quantity;
    }
    
    // تجنب فقدان الدقة العشرية وضمان 3 منازل عشرية
    return Math.Round(result, 3, MidpointRounding.AwayFromZero);
}
```

#### ب. دالة `CostPriceForBasicUnit`:
```csharp
static decimal CostPriceForBasicUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal costPrice)
{
    if (IsBasicUnit)
    {
        return costPrice;
    }
    
    // تجنب فقدان الدقة العشرية عند القسمة
    // استخدام Math.Round للحفاظ على 3 منازل عشرية كما هو محدد في قاعدة البيانات
    var result = costPrice / Quantity;
    return Math.Round(result, 3, MidpointRounding.AwayFromZero);
}
```

#### ج. دالة `QuantityUnit`:
```csharp
static decimal QuantityUnit(bool IsBasicUnit, bool IsBigger, decimal Quantity, decimal quantity)
{
    if (IsBasicUnit)
    {
        return quantity;
    }
    
    decimal result;
    if (IsBigger)
    {
        result = quantity * Quantity;
    }
    else
    {
        result = quantity / Quantity;
    }
    
    // تجنب فقدان الدقة العشرية وضمان 3 منازل عشرية
    return Math.Round(result, 3, MidpointRounding.AwayFromZero);
}
```

## النتائج المتوقعة

### قبل الإصلاح:
- إدخال سعر شراء: 1.75
- القيمة المحفوظة: 1.703 (بسبب المتوسط المرجح وفقدان الدقة)

### بعد الإصلاح:
- إدخال سعر شراء: 1.75
- القيمة المحفوظة: 1.750 (الاحتفاظ بالسعر الفعلي المدخل)

## اختبار الإصلاح

### سيناريو الاختبار:
1. إنشاء صنف جديد
2. إدخال فاتورة شراء بسعر 1.75
3. حفظ الفاتورة
4. التحقق من أن `Item.CostPrice` = 1.750 في قاعدة البيانات
5. إدخال فاتورة شراء أخرى بسعر 2.25
6. التحقق من أن `Item.CostPrice` = 2.250 (آخر سعر شراء)

### اختبار الوحدات المختلفة:
1. إنشاء صنف بوحدات متعددة (كيلو، نصف كيلو، ربع كيلو)
2. إدخال فاتورة شراء بوحدة "نصف كيلو" بسعر 1.75
3. التحقق من أن السعر للوحدة الأساسية = 1.75 / 0.5 = 3.500
4. التحقق من عدم وجود أرقام عشرية غير مرغوب فيها

## ملاحظات مهمة

### 1. تغيير منطق التسعير:
- **قبل**: استخدام المتوسط المرجح لأسعار الشراء
- **بعد**: استخدام آخر سعر شراء

### 2. إمكانية العودة للمتوسط المرجح:
إذا كان المطلوب استخدام المتوسط المرجح، يمكن إلغاء التعليق عن السطر المناسب في `UpdateCost`.

### 3. دقة الأرقام العشرية:
جميع العمليات الحسابية تستخدم الآن `Math.Round` مع 3 منازل عشرية لضمان التوافق مع إعدادات قاعدة البيانات.

## الملفات المعدلة

1. `PosGTech.DataAccess\Repository\ItemRepository.cs` - إصلاح منطق تحديث الأسعار
2. `PosGTech.ModelsDTO\Items\ItemDTO.cs` - إصلاح دقة العمليات الحسابية

## التأثير على النظام

### إيجابي:
- أسعار الشراء تحفظ بالدقة الصحيحة
- عدم تغيير الأسعار المدخلة بواسطة المستخدم
- ثبات في حسابات الوحدات

### يجب مراعاته:
- تغيير في منطق التسعير من المتوسط المرجح إلى آخر سعر
- قد يحتاج المستخدمون إلى فهم المنطق الجديد
