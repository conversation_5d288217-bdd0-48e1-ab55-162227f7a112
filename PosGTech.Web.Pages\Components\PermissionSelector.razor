@using PosGTech.ModelsDTO.Authorization

<MudContainer Class="pa-0">
    @if (!string.IsNullOrEmpty(Title))
    {
        <MudText Typo="Typo.body2" Class="mb-2 font-weight-medium">@Title</MudText>
    }

    <div style="max-height: @MaxHeight; overflow-y: auto;" class="permission-selector-container">
        @if (PermissionGroups.Groups?.Any() == true)
        {
            <MudExpansionPanels Elevation="0" Class="permission-groups">
                @foreach (var group in PermissionGroups.Groups)
                {
                    var groupPermissions = GetGroupPermissions(group.Value);
                    if (groupPermissions.Any())
                    {
                        <MudExpansionPanel Text="@group.Key" Icon="@GetGroupIcon(group.Key)" Class="permission-group-panel">
                            <div class="permission-items">
                                @foreach (var permission in groupPermissions)
                                {
                                    <MudCheckBox T="bool"
                                                 Value="@GetPermissionChecked(permission)"
                                                 ValueChanged="@((bool value) => OnPermissionChanged(permission, value))"
                                                 Label="@PermissionDescriptions.GetDescription(permission)"
                                                 Color="Color.Primary"
                                                 Class="permission-checkbox"
                                                 Dense="true" />
                                }
                            </div>
                        </MudExpansionPanel>
                    }
                }
            </MudExpansionPanels>
        }
        else
        {
            <MudAlert Severity="Severity.Info" Dense="true">
                <MudText Typo="Typo.body2">لا توجد صلاحيات متاحة</MudText>
            </MudAlert>
        }
    </div>

    @if (ShowSelectedCount && SelectedPermissions?.Any() == true)
    {
        <div class="mt-2">
            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                المختار: @SelectedPermissions.Count
            </MudChip>
        </div>
    }
</MudContainer>

<style>
    .permission-selector-container {
        border: 1px solid var(--mud-palette-lines-default);
        border-radius: 4px;
        padding: 8px;
    }

    .permission-groups .mud-expand-panel {
        margin-bottom: 4px;
    }

    .permission-group-panel .mud-expand-panel-header {
        padding: 8px 12px;
        min-height: 40px;
    }

    .permission-items {
        padding: 8px 0;
    }

    .permission-checkbox {
        margin: 2px 0;
    }

    .permission-checkbox .mud-checkbox-label {
        font-size: 0.875rem;
    }
</style>

@code {
    [Parameter] public List<string> SelectedPermissions { get; set; } = new();
    [Parameter] public EventCallback<List<string>> SelectedPermissionsChanged { get; set; }
    [Parameter] public List<string> ExcludedPermissions { get; set; } = new(); // صلاحيات لا تظهر
    [Parameter] public List<string> IncludedPermissions { get; set; } = new(); // صلاحيات محددة فقط
    [Parameter] public string Title { get; set; } = "";
    [Parameter] public string MaxHeight { get; set; } = "400px";
    [Parameter] public bool ShowSelectedCount { get; set; } = true;

    // Property للـ binding
    public List<string> Value
    {
        get => SelectedPermissions;
        set
        {
            if (SelectedPermissions != value)
            {
                SelectedPermissions = value;
                SelectedPermissionsChanged.InvokeAsync(value);
            }
        }
    }

    /// <summary>
    /// تحديد الصلاحيات التي يجب عرضها في المجموعة
    /// </summary>
    private List<string> GetGroupPermissions(List<string> groupPermissions)
    {
        var permissions = groupPermissions;

        // إذا كان هناك صلاحيات محددة فقط، اعرض منها فقط
        if (IncludedPermissions?.Any() == true)
        {
            permissions = permissions.Intersect(IncludedPermissions).ToList();
        }

        // استبعد الصلاحيات المحددة للاستبعاد
        if (ExcludedPermissions?.Any() == true)
        {
            permissions = permissions.Except(ExcludedPermissions).ToList();
        }

        return permissions;
    }

    /// <summary>
    /// التحقق من أن الصلاحية مختارة
    /// </summary>
    private bool GetPermissionChecked(string permission)
    {
        return SelectedPermissions?.Contains(permission) == true;
    }

    /// <summary>
    /// معالج تغيير حالة الصلاحية
    /// </summary>
    private async Task OnPermissionChanged(string permission, bool isChecked)
    {
        if (SelectedPermissions == null)
            SelectedPermissions = new List<string>();

        if (isChecked)
        {
            if (!SelectedPermissions.Contains(permission))
                SelectedPermissions.Add(permission);
        }
        else
        {
            SelectedPermissions.Remove(permission);
        }

        await SelectedPermissionsChanged.InvokeAsync(SelectedPermissions);
        StateHasChanged();
    }

    /// <summary>
    /// الحصول على أيقونة المجموعة
    /// </summary>
    private string GetGroupIcon(string groupName)
    {
        return groupName switch
        {
            "إدارة المستخدمين" => Icons.Material.Filled.People,
            "إدارة الأصناف" => Icons.Material.Filled.Category,
            "إدارة المبيعات" => Icons.Material.Filled.PointOfSale,
            "إدارة المشتريات" => Icons.Material.Filled.ShoppingCart,
            "إدارة المخزون" => Icons.Material.Filled.Inventory,
            "إدارة الحسابات" => Icons.Material.Filled.AccountBalance,
            "إدارة العملاء" => Icons.Material.Filled.Person,
            "إدارة الموردين" => Icons.Material.Filled.Business,
            "إدارة الخزائن" => Icons.Material.Filled.AccountBalanceWallet,
            "إدارة التقارير" => Icons.Material.Filled.Assessment,
            "إدارة النظام" => Icons.Material.Filled.Settings,
            _ => Icons.Material.Filled.Security
        };
    }
}
