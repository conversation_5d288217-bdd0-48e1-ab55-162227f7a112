using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Authentication
{
    /// <summary>
    /// نموذج بيانات تسجيل الدخول
    /// </summary>
    public class LoginDTO
    {
        /// <summary>
        /// اسم المستخدم أو البريد الإلكتروني
        /// </summary>
        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        public string UserName { get; set; }

        /// <summary>
        /// كلمة المرور
        /// </summary>
        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [DataType(DataType.Password)]
        public string Password { get; set; }

        /// <summary>
        /// تذكرني
        /// </summary>
        public bool RememberMe { get; set; } = false;
    }
}
