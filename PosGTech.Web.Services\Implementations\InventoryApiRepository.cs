using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Inventories;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للجرد - يحتوي على جميع العمليات المطلوبة للتعامل مع الجرد
    /// </summary>
    public class InventoryApiRepository : IInventoryApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<InventoryApiRepository> _logger;

        public InventoryApiRepository(HttpClient httpClient, ILogger<InventoryApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الجرد
        /// </summary>
        public async Task<(IEnumerable<InventoryDTO>? list, ResponseVM? response)> GetAllInventoriesAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الجرد");
                var response = await _httpClient.GetAsync("Inventories/getAllInventories");

                if (response.IsSuccessStatusCode)
                {
                    var inventories = await response.Content.ReadFromJsonAsync<InventoryDTO[]>();
                    return (inventories, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الجرد");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جرد بالمعرف
        /// </summary>
        public async Task<(InventoryDTO? model, ResponseVM? response)> GetInventoryByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب الجرد بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Inventories/getInventoryById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var inventory = await response.Content.ReadFromJsonAsync<InventoryDTO>();
                    return (inventory, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الجرد بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة جرد جديد
        /// </summary>
        public async Task<ResponseVM> InsertInventoryAsync(InventoryDTO inventory)
        {
            try
            {
                _logger.LogInformation("إضافة جرد جديد");
                var response = await _httpClient.PostAsJsonAsync("Inventories/insertInventory", inventory);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة الجرد بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الجرد");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث جرد موجود
        /// </summary>
        public async Task<ResponseVM> UpdateInventoryAsync(Guid id, InventoryDTO inventory)
        {
            try
            {
                _logger.LogInformation("تحديث الجرد: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Inventories/updateInventory/{id}", inventory);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث الجرد بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الجرد: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف جرد
        /// </summary>
        public async Task<ResponseVM> DeleteInventoryAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف الجرد: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Inventories/deleteInventory/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف الجرد بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الجرد: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب عناصر الجرد
        /// </summary>
        public async Task<(IEnumerable<InventoryItemDTO>? list, ResponseVM? response)> GetInventoryItemsAsync(Guid inventoryId)
        {
            try
            {
                _logger.LogInformation("جلب عناصر الجرد: {InventoryId}", inventoryId);
                var response = await _httpClient.GetAsync($"Inventories/getInventoryItems/{inventoryId}");

                if (response.IsSuccessStatusCode)
                {
                    var inventoryItems = await response.Content.ReadFromJsonAsync<InventoryItemDTO[]>();
                    return (inventoryItems, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب عناصر الجرد: {InventoryId}", inventoryId);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}
