using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Receipts;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IReceiptApiRepository
    {
        /// <summary>
        /// جلب جميع الإيصالات
        /// </summary>
        Task<(IEnumerable<ReceiptDTO>? list, ResponseVM? response)> GetAllReceiptsAsync();

        /// <summary>
        /// جلب إيصال بالمعرف
        /// </summary>
        Task<(ReceiptDTO? model, ResponseVM? response)> GetReceiptByIdAsync(Guid id);

        /// <summary>
        /// إضافة إيصال جديد
        /// </summary>
        Task<ResponseVM> InsertReceiptAsync(ReceiptDTO receipt);

        /// <summary>
        /// تحديث إيصال موجود
        /// </summary>
        Task<ResponseVM> UpdateReceiptAsync(Guid id, ReceiptDTO receipt);

        /// <summary>
        /// حذف إيصال
        /// </summary>
        Task<ResponseVM> DeleteReceiptAsync(Guid id);
    }
}
