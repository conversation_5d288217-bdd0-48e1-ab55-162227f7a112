namespace PosGTech.ModelsDTO.Authorization;

/// <summary>
/// فئة تحتوي على الأوصاف العربية لجميع الصلاحيات
/// </summary>
public static class PermissionDescriptions
{
    /// <summary>
    /// قاموس يربط كل صلاحية بوصفها العربي
    /// </summary>
    public static readonly Dictionary<string, string> Descriptions = new()
    {
        #region Items Management - إدارة الأصناف
        { PermissionConstants.ItemsView, "عرض الأصناف" },
        { PermissionConstants.ItemsAdd, "إضافة صنف جديد" },
        { PermissionConstants.ItemsEdit, "تعديل الأصناف" },
        { PermissionConstants.ItemsDelete, "حذف الأصناف" },
        #endregion

        #region Categories Management - إدارة التصنيفات
        { PermissionConstants.CategoriesView, "عرض التصنيفات" },
        { PermissionConstants.CategoriesAdd, "إضافة تصنيف جديد" },
        { PermissionConstants.CategoriesEdit, "تعديل التصنيفات" },
        { PermissionConstants.CategoriesDelete, "حذف التصنيفات" },
        #endregion

        #region Units Management - إدارة الوحدات
        { PermissionConstants.UnitsView, "عرض الوحدات" },
        { PermissionConstants.UnitsAdd, "إضافة وحدة جديدة" },
        { PermissionConstants.UnitsEdit, "تعديل الوحدات" },
        { PermissionConstants.UnitsDelete, "حذف الوحدات" },
        #endregion

        #region Purchases Management - إدارة المشتريات
        { PermissionConstants.PurchasesView, "عرض فواتير المشتريات" },
        { PermissionConstants.PurchasesAdd, "إضافة فاتورة مشتريات" },
        { PermissionConstants.PurchasesEdit, "تعديل فواتير المشتريات" },
        { PermissionConstants.PurchasesDelete, "حذف فواتير المشتريات" },
        #endregion

        #region Sales Management - إدارة المبيعات
        { PermissionConstants.SalesView, "عرض فواتير المبيعات" },
        { PermissionConstants.SalesAdd, "إضافة فاتورة مبيعات" },
        { PermissionConstants.SalesEdit, "تعديل فواتير المبيعات" },
        { PermissionConstants.SalesDelete, "حذف فواتير المبيعات" },
        #endregion

        #region Clients Management - إدارة العملاء
        { PermissionConstants.ClientsView, "عرض العملاء والموردين" },
        { PermissionConstants.ClientsAdd, "إضافة عميل أو مورد جديد" },
        { PermissionConstants.ClientsEdit, "تعديل بيانات العملاء والموردين" },
        { PermissionConstants.ClientsDelete, "حذف العملاء والموردين" },
        #endregion

        #region Employees Management - إدارة الموظفين
        { PermissionConstants.EmployeesView, "عرض الموظفين" },
        { PermissionConstants.EmployeesAdd, "إضافة موظف جديد" },
        { PermissionConstants.EmployeesEdit, "تعديل بيانات الموظفين" },
        { PermissionConstants.EmployeesDelete, "حذف الموظفين" },
        #endregion

        #region Stores Management - إدارة المخازن
        { PermissionConstants.StoresView, "عرض المخازن" },
        { PermissionConstants.StoresAdd, "إضافة مخزن جديد" },
        { PermissionConstants.StoresEdit, "تعديل المخازن" },
        { PermissionConstants.StoresDelete, "حذف المخازن" },
        #endregion

        #region Treasuries Management - إدارة الخزائن
        { PermissionConstants.TreasuriesView, "عرض الخزائن" },
        { PermissionConstants.TreasuriesAdd, "إضافة خزينة جديدة" },
        { PermissionConstants.TreasuriesEdit, "تعديل الخزائن" },
        { PermissionConstants.TreasuriesDelete, "حذف الخزائن" },
        #endregion

        #region Inventory Management - إدارة الجرد
        { PermissionConstants.InventoryView, "عرض عمليات الجرد" },
        { PermissionConstants.InventoryAdd, "إضافة عملية جرد جديدة" },
        { PermissionConstants.InventoryEdit, "تعديل عمليات الجرد" },
        { PermissionConstants.InventoryDelete, "حذف عمليات الجرد" },
        #endregion

        #region Receipts Management - إدارة السندات
        { PermissionConstants.ReceiptsView, "عرض السندات المالية" },
        { PermissionConstants.ReceiptsAdd, "إضافة سند مالي جديد" },
        { PermissionConstants.ReceiptsEdit, "تعديل السندات المالية" },
        { PermissionConstants.ReceiptsDelete, "حذف السندات المالية" },
        #endregion

        #region Expenses Management - إدارة المصروفات
        { PermissionConstants.ExpensesView, "عرض المصروفات" },
        { PermissionConstants.ExpensesAdd, "إضافة مصروف جديد" },
        { PermissionConstants.ExpensesEdit, "تعديل المصروفات" },
        { PermissionConstants.ExpensesDelete, "حذف المصروفات" },
        #endregion

        #region Reports Management - إدارة التقارير
        { PermissionConstants.ReportsView, "عرض التقارير" },
        { PermissionConstants.ReportsGenerate, "إنشاء التقارير" },
        { PermissionConstants.ReportsExport, "تصدير التقارير" },
        { PermissionConstants.ReportsAdvanced, "التقارير المتقدمة" },
        #endregion

        #region Users Management - إدارة المستخدمين
        { PermissionConstants.UsersView, "عرض المستخدمين" },
        { PermissionConstants.UsersAdd, "إضافة مستخدم جديد" },
        { PermissionConstants.UsersEdit, "تعديل المستخدمين" },
        { PermissionConstants.UsersDelete, "حذف المستخدمين" },
        #endregion

        #region Roles Management - إدارة الأدوار
        { PermissionConstants.RolesView, "عرض الأدوار" },
        { PermissionConstants.RolesAdd, "إضافة دور جديد" },
        { PermissionConstants.RolesEdit, "تعديل الأدوار" },
        { PermissionConstants.RolesDelete, "حذف الأدوار" },
        #endregion

        #region System Administration - إدارة النظام
        { PermissionConstants.SystemSettings, "إعدادات النظام" },
        { PermissionConstants.SystemBackup, "نسخ احتياطي للنظام" },
        { PermissionConstants.SystemRestore, "استعادة النظام" },
        { PermissionConstants.SystemLogs, "سجلات النظام" },
        #endregion
    };

    /// <summary>
    /// الحصول على الوصف العربي لصلاحية معينة
    /// </summary>
    /// <param name="permission">الصلاحية</param>
    /// <returns>الوصف العربي أو اسم الصلاحية إذا لم يوجد وصف</returns>
    public static string GetDescription(string permission)
    {
        return Descriptions.TryGetValue(permission, out var description) ? description : permission;
    }

    /// <summary>
    /// الحصول على جميع الصلاحيات مع أوصافها
    /// </summary>
    /// <returns>قاموس يحتوي على الصلاحيات وأوصافها</returns>
    public static Dictionary<string, string> GetAllPermissionsWithDescriptions()
    {
        return Descriptions.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }
}
