using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Items
{
    public class PaginatedItemsDTO
    {
        public IEnumerable<ItemDTO> Items { get; set; } = new List<ItemDTO>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasNextPage { get; set; }
        public bool HasPreviousPage { get; set; }
    }

    public class ItemSearchRequestDTO
    {
        public string? Search { get; set; }
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
}
