using PosGTech.ModelsDTO.Clients;
using PosGTech.ModelsDTO.Stores;

namespace PosGTech.ModelsDTO.Sells
{
    public class SalesReportDTO
    {
        public SalesStatisticsDTO Statistics { get; set; } = new();
        public List<SalesReportDetailDTO> SalesDetails { get; set; } = new();
        public List<DailySummaryDTO> DailySummary { get; set; } = new();
    }

    public class SalesStatisticsDTO
    {
        public decimal TotalRevenue { get; set; }
        public int TotalInvoices { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public int TotalItems { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalRemaining { get; set; }
    }

    public class SalesReportDetailDTO
    {
        public Guid Id { get; set; }
        public int InvoiceNo { get; set; }
        public DateOnly Date { get; set; }
        public ClientCMDTO Client { get; set; } = new();
        public string StoreName { get; set; } = string.Empty;
        public decimal Total { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal FinalTotal { get; set; }
        public decimal Paid { get; set; }
        public decimal Remaining { get; set; }
        public int ItemCount { get; set; }
    }

    public class DailySummaryDTO
    {
        public DateOnly Date { get; set; }
        public int InvoiceCount { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalDiscount { get; set; }
        public decimal FinalTotal { get; set; }
        public int ItemCount { get; set; }
        public decimal AverageInvoiceValue { get; set; }
    }
}
