@page "/upsertRole/{id:guid}"
@using PosGTech.ModelsDTO.Roles
@using PosGTech.ModelsDTO.Authorization
@using PosGTech.Web.Pages.Components

<PageTitle>@pageTitle</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <!-- Breadcrumb -->
    <MudBreadcrumbs Items="_items" Class="mb-4" />

    <EditForm Model="@roleModel" OnValidSubmit="@SaveRole">
        <DataAnnotationsValidator />
        
        <!-- Header Section -->
        <MudPaper Elevation="2" Class="pa-4 mb-4 rounded-lg">
            <MudText Typo="Typo.h5" Class="mb-3">
                <MudIcon Icon="@Icons.Material.Filled.AdminPanelSettings" Class="ml-2" />
                @pageTitle
            </MudText>

            <MudGrid>
                <!-- معلومات الدور الأساسية -->
                <MudItem xs="12" md="6">
                    <MudTextField @bind-Value="roleModel.Name"
                                  Label="اسم الدور"
                                  Variant="Variant.Outlined"
                                  Required="true"
                                  For="@(() => roleModel.Name)"
                                  Disabled="@(IsEdit && IsSystemRole)"
                                  Class="mb-3" />

                    <MudTextField @bind-Value="roleModel.Description"
                                  Label="وصف الدور"
                                  Variant="Variant.Outlined"
                                  Lines="3"
                                  For="@(() => roleModel.Description)"
                                  Class="mb-3" />
                </MudItem>

                <!-- معلومات إضافية -->
                <MudItem xs="12" md="6">
                    <MudPaper Elevation="1" Class="pa-3 rounded-lg info-panel">
                        <MudText Typo="Typo.subtitle1" Class="mb-2 font-weight-bold">
                            <MudIcon Icon="@Icons.Material.Filled.Info" Class="ml-1" />
                            معلومات الدور
                        </MudText>

                        @if (IsEdit)
                        {
                            <div class="mb-2">
                                <MudText Typo="Typo.body2" Class="text-muted">نوع الدور:</MudText>
                                <MudChip T="string" Size="Size.Small" Color="@(IsSystemRole ? Color.Success : Color.Info)">
                                    @(IsSystemRole ? "دور أساسي" : "دور مخصص")
                                </MudChip>
                            </div>

                            @if (IsEdit)
                            {
                                <div class="mb-2">
                                    <MudText Typo="Typo.body2" Class="text-muted">عدد المستخدمين:</MudText>
                                    <MudText Typo="Typo.body1">0 مستخدم</MudText>
                                </div>
                            }
                        }

                        <div class="mb-2">
                            <MudText Typo="Typo.body2" Class="text-muted">عدد الصلاحيات المختارة:</MudText>
                            <MudText Typo="Typo.body1">@(roleModel.Permissions?.Count ?? 0) صلاحية</MudText>
                        </div>
                    </MudPaper>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- قسم اختيار الصلاحيات -->
        <MudPaper Elevation="2" Class="pa-4 mb-4 rounded-lg">
            <MudText Typo="Typo.h6" Class="mb-3">
                <MudIcon Icon="@Icons.Material.Filled.Security" Class="ml-2" />
                صلاحيات الدور
            </MudText>

            <MudAlert Severity="Severity.Info" Class="mb-3" Dense="true">
                <MudText Typo="Typo.body2">
                    اختر الصلاحيات التي تريد منحها لهذا الدور. يمكن للمستخدمين الحصول على صلاحيات إضافية أو أقل من خلال إعدادات المستخدم الفردية.
                </MudText>
            </MudAlert>

            <!-- أزرار التحكم السريع -->
            <div class="mb-3">
                <MudButtonGroup Variant="Variant.Outlined" Size="Size.Small">
                    <MudButton OnClick="SelectAllPermissions"
                               StartIcon="@Icons.Material.Filled.SelectAll"
                               Color="Color.Success">
                        تحديد الكل
                    </MudButton>
                    <MudButton OnClick="ClearAllPermissions"
                               StartIcon="@Icons.Material.Filled.Clear"
                               Color="Color.Warning">
                        إلغاء التحديد
                    </MudButton>
                    <MudButton OnClick="@(() => showPresetRoles = !showPresetRoles)"
                               StartIcon="@Icons.Material.Filled.Bookmark"
                               Color="Color.Info">
                        قوالب جاهزة
                    </MudButton>
                </MudButtonGroup>
            </div>

            <!-- قوالب الأدوار الجاهزة -->
            @if (showPresetRoles)
            {
                <MudPaper Elevation="1" Class="pa-3 mb-3 rounded-lg preset-roles-panel">
                    <MudText Typo="Typo.subtitle2" Class="mb-2">قوالب أدوار جاهزة:</MudText>
                    <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                        <MudButton OnClick="@(() => ApplyRolePreset("SalesEmployee"))"
                                   Color="Color.Primary">
                            موظف مبيعات
                        </MudButton>
                        <MudButton OnClick="@(() => ApplyRolePreset("PurchaseManager"))"
                                   Color="Color.Primary">
                            مدير مشتريات
                        </MudButton>
                        <MudButton OnClick="@(() => ApplyRolePreset("Accountant"))"
                                   Color="Color.Primary">
                            محاسب
                        </MudButton>
                        <MudButton OnClick="@(() => ApplyRolePreset("InventoryManager"))"
                                   Color="Color.Primary">
                            مدير مخزون
                        </MudButton>
                    </MudButtonGroup>
                </MudPaper>
            }

            <!-- مكون اختيار الصلاحيات -->
            <PermissionSelector SelectedPermissions="roleModel.Permissions"
                                SelectedPermissionsChanged="@((List<string> permissions) => roleModel.Permissions = permissions)"
                                Title=""
                                MaxHeight="500px"
                                ShowSelectedCount="true" />
        </MudPaper>

        <!-- أزرار الحفظ والإلغاء -->
        <MudPaper Elevation="2" Class="pa-4 rounded-lg">
            <MudStack Row="true" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                <div>
                    @if (IsEdit && IsSystemRole)
                    {
                        <MudAlert Severity="Severity.Warning" Dense="true" Class="mb-0">
                            <MudText Typo="Typo.body2">
                                هذا دور أساسي في النظام. لا يمكن تغيير اسمه ولكن يمكن تعديل صلاحياته.
                            </MudText>
                        </MudAlert>
                    }
                </div>

                <MudButtonGroup Variant="Variant.Filled">
                    <MudButton ButtonType="ButtonType.Submit"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Save"
                               Disabled="@isLoading">
                        @if (isLoading)
                        {
                            <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                            <span class="ml-2">جاري الحفظ...</span>
                        }
                        else
                        {
                            <span>حفظ</span>
                        }
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Cancel"
                               OnClick="@(() => _navigation.NavigateTo("/roles"))"
                               Disabled="@isLoading">
                        إلغاء
                    </MudButton>
                </MudButtonGroup>
            </MudStack>
        </MudPaper>
    </EditForm>
</MudContainer>

<style>
    .info-panel {
        background-color: var(--mud-palette-background-grey);
    }

    .preset-roles-panel {
        background-color: var(--mud-palette-info-lighten);
        border-left: 4px solid var(--mud-palette-info);
    }

    .permission-summary {
        background: linear-gradient(135deg, var(--mud-palette-primary-lighten) 0%, var(--mud-palette-primary) 100%);
        color: white;
    }
</style>


