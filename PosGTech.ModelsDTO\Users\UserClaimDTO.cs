using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Users
{
    /// <summary>
    /// نموذج بيانات لإدارة UserClaim للصلاحيات الإضافية والمحذوفة
    /// </summary>
    public class UserClaimDTO
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        [Required(ErrorMessage = "معرف المستخدم مطلوب")]
        public Guid UserId { get; set; }

        /// <summary>
        /// نوع الـ Claim
        /// "additional_permission" للصلاحيات الإضافية
        /// "removed_permission" للصلاحيات المحذوفة من الدور
        /// </summary>
        [Required(ErrorMessage = "نوع الصلاحية مطلوب")]
        public string ClaimType { get; set; }

        /// <summary>
        /// قيمة الـ Claim (اسم الصلاحية)
        /// مثال: "Items.Add", "Sales.Delete"
        /// </summary>
        [Required(ErrorMessage = "اسم الصلاحية مطلوب")]
        public string ClaimValue { get; set; }

        /// <summary>
        /// تاريخ إضافة الصلاحية
        /// </summary>
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// المستخدم الذي قام بإضافة الصلاحية
        /// </summary>
        public Guid? AssignedBy { get; set; }

        /// <summary>
        /// وصف الصلاحية بالعربية (للعرض فقط)
        /// </summary>
        public string? PermissionDescription { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لإدارة صلاحيات المستخدم بشكل مجمع
    /// </summary>
    public class UserPermissionManagementDTO
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        [Required(ErrorMessage = "معرف المستخدم مطلوب")]
        public Guid UserId { get; set; }

        /// <summary>
        /// الصلاحيات المراد إضافتها كصلاحيات إضافية
        /// </summary>
        public List<string> PermissionsToAdd { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات الإضافية المراد إزالتها
        /// </summary>
        public List<string> AdditionalPermissionsToRemove { get; set; } = new List<string>();

        /// <summary>
        /// صلاحيات الدور المراد حذفها (إضافة كـ removed_permission)
        /// </summary>
        public List<string> RolePermissionsToRemove { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات المحذوفة من الدور المراد استعادتها
        /// </summary>
        public List<string> RemovedPermissionsToRestore { get; set; } = new List<string>();

        /// <summary>
        /// المستخدم الذي يقوم بالتعديل
        /// </summary>
        public Guid? ModifiedBy { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لعرض الصلاحيات الفعلية للمستخدم
    /// </summary>
    public class UserEffectivePermissionsDTO
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// الدور الأساسي للمستخدم
        /// </summary>
        public string PrimaryRole { get; set; }

        /// <summary>
        /// صلاحيات الدور الأساسي
        /// </summary>
        public List<string> RolePermissions { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات الإضافية
        /// </summary>
        public List<string> AdditionalPermissions { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات المحذوفة من الدور
        /// </summary>
        public List<string> RemovedPermissions { get; set; } = new List<string>();

        /// <summary>
        /// الصلاحيات الفعلية النهائية
        /// = RolePermissions + AdditionalPermissions - RemovedPermissions
        /// </summary>
        public List<string> EffectivePermissions { get; set; } = new List<string>();

        /// <summary>
        /// تاريخ آخر تحديث للصلاحيات
        /// </summary>
        public DateTime LastModified { get; set; }
    }

    /// <summary>
    /// نموذج بيانات لتتبع تاريخ تغييرات الصلاحيات
    /// </summary>
    public class UserPermissionHistoryDTO
    {
        /// <summary>
        /// معرف المستخدم
        /// </summary>
        public Guid UserId { get; set; }

        /// <summary>
        /// نوع العملية (إضافة/حذف)
        /// </summary>
        public string Action { get; set; } // "Added", "Removed"

        /// <summary>
        /// نوع الصلاحية
        /// </summary>
        public string PermissionType { get; set; } // "Additional", "Removed"

        /// <summary>
        /// اسم الصلاحية
        /// </summary>
        public string Permission { get; set; }

        /// <summary>
        /// تاريخ العملية
        /// </summary>
        public DateTime ActionDate { get; set; }

        /// <summary>
        /// المستخدم الذي قام بالعملية
        /// </summary>
        public Guid ActionBy { get; set; }

        /// <summary>
        /// ملاحظات إضافية
        /// </summary>
        public string? Notes { get; set; }
    }
}
