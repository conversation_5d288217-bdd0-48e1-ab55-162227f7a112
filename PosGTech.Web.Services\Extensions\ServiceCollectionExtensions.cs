using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PosGTech.Web.Services.Configuration;
using PosGTech.Web.Services.Implementations;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Headers;
using Blazored.LocalStorage;


namespace PosGTech.Web.Services.Extensions
{
    /// <summary>
    /// طرق تمديد لتسجيل خدمات API في Dependency Injection
    /// </summary>
    public static class ServiceCollectionExtensions
    {
       
        public static IServiceCollection AddUnitOfWork(this IServiceCollection services)
        {
            services.AddScoped<IUnitOfWork>(serviceProvider =>
            {
                var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
                var httpClient = httpClientFactory.CreateClient("ApiClient");
                var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
                var localStorage = serviceProvider.GetRequiredService<ILocalStorageService>();

                return new UnitOfWork(httpClient, loggerFactory, localStorage);
            });

            return services;
        }
    }
}
