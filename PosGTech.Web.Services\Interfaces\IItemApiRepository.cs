using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Items;

namespace PosGTech.Web.Services.Interfaces
{
    /// <summary>
    /// واجهة مستودع API للأصناف - تحتوي على جميع العمليات المطلوبة للتعامل مع الأصناف
    /// </summary>
    public interface IItemApiRepository
    {
        /// <summary>
        /// جلب جميع الأصناف مع التفاصيل الكاملة
        /// </summary>
        /// <returns>قائمة الأصناف مع حالة الاستجابة</returns>
        Task<(IEnumerable<ItemDTO>? list, ResponseVM? response)> GetAllItemsAsync();

        /// <summary>
        /// جلب جميع الأصناف للمشتريات
        /// </summary>
        /// <returns>قائمة الأصناف للمشتريات مع حالة الاستجابة</returns>
        Task<(IEnumerable<ItemDTO>? list, ResponseVM? response)> GetAllItemsForPurchaseAsync();

        /// <summary>
        /// جلب جميع الأصناف للقوائم المنسدلة
        /// </summary>
        /// <returns>قائمة الأصناف المبسطة مع حالة الاستجابة</returns>
        Task<(IEnumerable<ItemCMDTO>? list, ResponseVM? response)> GetAllItemsCMAsync();

        /// <summary>
        /// جلب صنف بالمعرف
        /// </summary>
        /// <param name="id">معرف الصنف</param>
        /// <returns>بيانات الصنف مع حالة الاستجابة</returns>
        Task<(ItemDTO? model, ResponseVM? response)> GetItemByIdAsync(Guid id);

        /// <summary>
        /// إضافة صنف جديد
        /// </summary>
        /// <param name="item">بيانات الصنف الجديد</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> InsertItemAsync(ItemDTO item);

        /// <summary>
        /// تحديث صنف موجود
        /// </summary>
        /// <param name="id">معرف الصنف</param>
        /// <param name="item">بيانات الصنف المحدثة</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> UpdateItemAsync(Guid id, ItemDTO item);

        /// <summary>
        /// حذف صنف
        /// </summary>
        /// <param name="id">معرف الصنف</param>
        /// <returns>حالة الاستجابة</returns>
        Task<ResponseVM> DeleteItemAsync(Guid id);
    }
}
