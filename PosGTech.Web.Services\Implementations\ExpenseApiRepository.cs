using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Expenses;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمصروفات - يحتوي على جميع العمليات المطلوبة للتعامل مع المصروفات
    /// </summary>
    public class ExpenseApiRepository : IExpenseApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ExpenseApiRepository> _logger;

        public ExpenseApiRepository(HttpClient httpClient, ILogger<ExpenseApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع المصروفات
        /// </summary>
        public async Task<(IEnumerable<ExpenseDTO>? list, ResponseVM? response)> GetAllExpensesAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع المصروفات");

                var httpResponse = await _httpClient.GetAsync("Expenses/getAllExpenses");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var expenses = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<ExpenseDTO>>();
                    _logger.LogInformation("تم جلب {Count} مصروف بنجاح", expenses?.Count() ?? 0);
                    return (expenses, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب المصروفات: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب المصروفات: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المصروفات");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب مصروف بالمعرف
        /// </summary>
        public async Task<(ExpenseDTO? model, ResponseVM? response)> GetExpenseByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب المصروف بالمعرف: {ExpenseId}", id);

                var httpResponse = await _httpClient.GetAsync($"Expenses/getExpenseById/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var expense = await httpResponse.Content.ReadFromJsonAsync<ExpenseDTO>();
                    _logger.LogInformation("تم جلب المصروف بالمعرف {ExpenseId} بنجاح", id);
                    return (expense, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب المصروف بالمعرف {ExpenseId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب المصروف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب المصروف بالمعرف: {ExpenseId}", id);
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// إضافة مصروف جديد
        /// </summary>
        public async Task<ResponseVM> InsertExpenseAsync(ExpenseDTO expense)
        {
            try
            {
                _logger.LogInformation("بدء عملية إضافة مصروف جديد: {ExpenseName}", expense.Name);

                var json = JsonSerializer.Serialize(expense);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PostAsync("Expenses/insertExpense", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم إضافة المصروف {ExpenseName} بنجاح", expense.Name);
                    return response ?? new ResponseVM { Message = "تم إضافة المصروف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في إضافة المصروف {ExpenseName}: {StatusCode} - {Error}", expense.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في إضافة المصروف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة المصروف: {ExpenseName}", expense.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// تحديث مصروف موجود
        /// </summary>
        public async Task<ResponseVM> UpdateExpenseAsync(Guid id, ExpenseDTO expense)
        {
            try
            {
                _logger.LogInformation("بدء عملية تحديث المصروف: {ExpenseId} - {ExpenseName}", id, expense.Name);

                var json = JsonSerializer.Serialize(expense);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PutAsync($"Expenses/updateExpense/{id}", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم تحديث المصروف {ExpenseId} - {ExpenseName} بنجاح", id, expense.Name);
                    return response ?? new ResponseVM { Message = "تم تحديث المصروف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في تحديث المصروف {ExpenseId} - {ExpenseName}: {StatusCode} - {Error}", id, expense.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في تحديث المصروف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث المصروف: {ExpenseId} - {ExpenseName}", id, expense.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// حذف مصروف
        /// </summary>
        public async Task<ResponseVM> DeleteExpenseAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية حذف المصروف: {ExpenseId}", id);

                var httpResponse = await _httpClient.DeleteAsync($"Expenses/deleteExpense/{id}");
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم حذف المصروف {ExpenseId} بنجاح", id);
                    return response ?? new ResponseVM { Message = "تم حذف المصروف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في حذف المصروف {ExpenseId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في حذف المصروف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء حذف المصروف: {ExpenseId}", id);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }
    }
}
