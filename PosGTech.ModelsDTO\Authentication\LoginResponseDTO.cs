namespace PosGTech.ModelsDTO.Authentication
{
    /// <summary>
    /// نموذج استجابة تسجيل الدخول
    /// </summary>
    public class LoginResponseDTO
    {
        /// <summary>
        /// رسالة الاستجابة
        /// </summary>
        public string? Message { get; set; }

        /// <summary>
        /// حالة المصادقة
        /// </summary>
        public bool IsAuthenticated { get; set; }

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// البريد الإلكتروني
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// الأدوار
        /// </summary>
        public List<string>? Roles { get; set; }

        /// <summary>
        /// رمز المصادقة
        /// </summary>
        public string? Token { get; set; }

        /// <summary>
        /// تاريخ انتهاء الرمز
        /// </summary>
        public DateTime? ExpiresOn { get; set; }

        /// <summary>
        /// رمز التحديث
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// تاريخ انتهاء رمز التحديث
        /// </summary>
        public DateTime RefreshTokenExpiration { get; set; }
    }
}
