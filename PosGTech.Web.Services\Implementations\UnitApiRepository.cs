using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Units;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للوحدات - يحتوي على جميع العمليات المطلوبة للتعامل مع الوحدات
    /// </summary>
    public class UnitApiRepository : IUnitApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<UnitApiRepository> _logger;

        public UnitApiRepository(HttpClient httpClient, ILogger<UnitApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الوحدات
        /// </summary>
        public async Task<(IEnumerable<UnitDTO>? list, ResponseVM? response)> GetAllUnitsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الوحدات");
                var response = await _httpClient.GetAsync("Units/getAllUnits");

                if (response.IsSuccessStatusCode)
                {
                    var units = await response.Content.ReadFromJsonAsync<UnitDTO[]>();
                    return (units, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب وحدة بالمعرف
        /// </summary>
        public async Task<(UnitDTO? model, ResponseVM? response)> GetUnitByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب الوحدة بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Units/getUnitById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var unit = await response.Content.ReadFromJsonAsync<UnitDTO>();
                    return (unit, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الوحدة بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة وحدة جديدة
        /// </summary>
        public async Task<ResponseVM> InsertUnitAsync(UnitDTO unit)
        {
            try
            {
                _logger.LogInformation("إضافة وحدة جديدة: {Name}", unit.Name);
                var response = await _httpClient.PostAsJsonAsync("Units/insertUnit", unit);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة الوحدة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الوحدة: {Name}", unit.Name);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث وحدة موجودة
        /// </summary>
        public async Task<ResponseVM> UpdateUnitAsync(Guid id, UnitDTO unit)
        {
            try
            {
                _logger.LogInformation("تحديث الوحدة: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Units/updateUnit/{id}", unit);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث الوحدة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الوحدة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف وحدة
        /// </summary>
        public async Task<ResponseVM> DeleteUnitAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف الوحدة: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Units/deleteUnit/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف الوحدة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الوحدة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}
