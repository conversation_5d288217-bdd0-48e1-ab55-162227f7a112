# اختبار إصلاح حقل سعر البيع في فواتير المشتريات

## خطوات الاختبار التفصيلية

### الاختبار الأول: جلب سعر البيع عند اختيار الصنف

#### التحضير:
1. تأكد من وجود أصناف في النظام بأسعار بيع محددة
2. افتح صفحة فواتير المشتريات
3. تأكد من أن حقل سعر البيع فارغ في البداية

#### خطوات الاختبار:
```
1. النقر على حقل "الصنف"
2. كتابة اسم صنف أو اختياره من القائمة المنسدلة
3. النقر على الصنف لاختياره
```

#### النتيجة المتوقعة:
```
✅ حقل "سعر البيع" يتم تعبئته تلقائياً بسعر البيع المحفوظ للصنف
✅ السعر المعروض يطابق السعر المحفوظ في قاعدة البيانات
✅ إذا كان الصنف بدون سعر بيع، يظهر 0
```

#### اختبار الوحدات المتعددة:
```
1. اختيار صنف له وحدات متعددة (مثل: كيلو، نصف كيلو، ربع كيلو)
2. تغيير الوحدة من القائمة المنسدلة
3. مراقبة تغيير سعر البيع حسب الوحدة
```

#### النتيجة المتوقعة للوحدات:
```
✅ سعر البيع يتغير تلقائياً عند تغيير الوحدة
✅ السعر يتم حسابه بشكل صحيح حسب نسبة الوحدة
```

### الاختبار الثاني: مسح حقل سعر البيع بعد إضافة الصنف

#### خطوات الاختبار:
```
1. اختيار صنف (يجب أن يتم تعبئة سعر البيع تلقائياً)
2. إدخال الكمية وسعر الشراء
3. النقر على زر "إضافة الصنف"
```

#### النتيجة المتوقعة:
```
✅ الصنف يتم إضافته لجدول الفاتورة
✅ حقل "الصنف" يتم مسحه
✅ حقل "سعر البيع" يتم مسحه (يصبح 0 أو فارغ)
✅ حقل "الكمية" يتم مسحه
✅ حقل "سعر الشراء" يتم مسحه
✅ التركيز يعود لحقل البحث عن الصنف
```

### الاختبار الثالث: مسح حقل سعر البيع بعد حفظ الفاتورة

#### خطوات الاختبار:
```
1. إضافة عدة أصناف للفاتورة
2. تعبئة بيانات الفاتورة (العميل، التاريخ، إلخ)
3. النقر على زر "حفظ"
4. انتظار رسالة "تم الحفظ بنجاح"
```

#### النتيجة المتوقعة:
```
✅ الفاتورة يتم حفظها بنجاح
✅ رسالة نجاح تظهر
✅ جميع حقول إدخال الصنف الجديد يتم مسحها:
   - حقل الصنف
   - حقل سعر البيع
   - حقل الكمية
   - حقل سعر الشراء
✅ التركيز يعود لحقل البحث عن الصنف
✅ الفاتورة جاهزة لإضافة أصناف جديدة
```

### الاختبار الرابع: مسح الحقول عند إنشاء فاتورة جديدة

#### خطوات الاختبار:
```
1. بعد حفظ فاتورة أو فتح فاتورة موجودة
2. النقر على زر "فاتورة جديدة"
3. انتظار رسالة "تم إنشاء فاتورة شراء جديدة"
```

#### النتيجة المتوقعة:
```
✅ فاتورة جديدة يتم إنشاؤها
✅ جميع حقول الفاتورة يتم مسحها
✅ جميع حقول إدخال الصنف يتم مسحها:
   - حقل الصنف
   - حقل سعر البيع
   - حقل الكمية
   - حقل سعر الشراء
✅ التاريخ يتم تعيينه للتاريخ الحالي
✅ جدول الأصناف فارغ
```

### الاختبار الخامس: حالات حدية

#### اختبار مسح اختيار الصنف:
```
1. اختيار صنف (سعر البيع يتم تعبئته)
2. مسح حقل الصنف أو اختيار "فارغ"
```

#### النتيجة المتوقعة:
```
✅ حقل سعر البيع يتم مسحه (يصبح 0)
✅ باقي الحقول تبقى كما هي
```

#### اختبار صنف بدون سعر بيع:
```
1. إنشاء صنف جديد بدون تحديد سعر بيع
2. اختيار هذا الصنف في فاتورة الشراء
```

#### النتيجة المتوقعة:
```
✅ حقل سعر البيع يظهر 0
✅ لا تظهر أخطاء
✅ يمكن إدخال سعر بيع يدوياً
```

#### اختبار تغيير الصنف:
```
1. اختيار صنف أول (سعر بيع = 10)
2. تغيير الاختيار لصنف ثاني (سعر بيع = 20)
```

#### النتيجة المتوقعة:
```
✅ سعر البيع يتغير من 10 إلى 20
✅ باقي الحقول تتحدث حسب الصنف الجديد
```

## سيناريوهات اختبار شاملة

### السيناريو الأول: دورة عمل كاملة
```
1. فتح صفحة فواتير المشتريات
2. اختيار صنف "أرز" (سعر بيع = 15.50)
   → التحقق: سعر البيع = 15.50
3. إدخال كمية = 10
4. إدخال سعر شراء = 12.00
5. النقر على "إضافة الصنف"
   → التحقق: جميع الحقول تم مسحها
6. اختيار صنف "سكر" (سعر بيع = 8.75)
   → التحقق: سعر البيع = 8.75
7. إدخال كمية = 5
8. إدخال سعر شراء = 7.00
9. النقر على "إضافة الصنف"
   → التحقق: جميع الحقول تم مسحها
10. تعبئة بيانات الفاتورة
11. النقر على "حفظ"
    → التحقق: الفاتورة محفوظة وجميع الحقول مسحت
```

### السيناريو الثاني: اختبار الوحدات المتعددة
```
1. إنشاء صنف "زيت" بوحدات:
   - لتر (وحدة أساسية) - سعر بيع = 20.00
   - نصف لتر - سعر بيع = 12.00
   - ربع لتر - سعر بيع = 7.00
2. اختيار صنف "زيت"
   → التحقق: سعر البيع = 20.00 (الوحدة الأساسية)
3. تغيير الوحدة إلى "نصف لتر"
   → التحقق: سعر البيع = 12.00
4. تغيير الوحدة إلى "ربع لتر"
   → التحقق: سعر البيع = 7.00
5. إضافة الصنف
   → التحقق: جميع الحقول مسحت
```

## أدوات الاختبار

### فحص قاعدة البيانات:
```sql
-- فحص أسعار البيع للأصناف
SELECT 
    i.Name as ItemName,
    iu.Name as UnitName,
    iu.SalePrice,
    iu.IsBasicUnit,
    iu.Quantity
FROM Items i
JOIN ItemUnits iu ON i.Id = iu.ItemId
ORDER BY i.Name, iu.IsBasicUnit DESC;
```

### فحص واجهة المستخدم:
```javascript
// فحص قيمة حقل سعر البيع في المتصفح
document.querySelector('input[label="سعر البيع"]').value
```

### فحص الشبكة (Network):
```
1. فتح أدوات المطور (F12)
2. الذهاب لتبويب Network
3. مراقبة طلبات API عند اختيار الأصناف
4. التأكد من جلب البيانات الصحيحة
```

## معايير النجاح والفشل

### ✅ معايير النجاح:
- [ ] سعر البيع يتم جلبه تلقائياً عند اختيار الصنف
- [ ] سعر البيع يتطابق مع القيمة المحفوظة في قاعدة البيانات
- [ ] سعر البيع يتغير عند تغيير الوحدة
- [ ] سعر البيع يتم مسحه بعد إضافة الصنف
- [ ] سعر البيع يتم مسحه بعد حفظ الفاتورة
- [ ] سعر البيع يتم مسحه عند إنشاء فاتورة جديدة
- [ ] لا توجد أخطاء في واجهة المستخدم
- [ ] الأداء مقبول (لا توجد بطء ملحوظ)

### ❌ معايير الفشل:
- [ ] سعر البيع لا يتم جلبه عند اختيار الصنف
- [ ] سعر البيع لا يتطابق مع القيمة المحفوظة
- [ ] سعر البيع لا يتم مسحه في الحالات المطلوبة
- [ ] ظهور أخطاء في واجهة المستخدم
- [ ] بطء في الاستجابة
- [ ] تأثير سلبي على وظائف أخرى

## تقرير الاختبار

### نموذج تقرير:
```
تاريخ الاختبار: ___________
المختبر: ___________
إصدار النظام: ___________

النتائج:
□ الاختبار الأول: ناجح / فاشل
□ الاختبار الثاني: ناجح / فاشل  
□ الاختبار الثالث: ناجح / فاشل
□ الاختبار الرابع: ناجح / فاشل
□ الاختبار الخامس: ناجح / فاشل

المشاكل المكتشفة:
1. ___________
2. ___________

التوصيات:
1. ___________
2. ___________

الحالة العامة: ناجح / يحتاج إصلاح / فاشل
```

## ملاحظات مهمة

1. **اختبر في متصفحات مختلفة**: Chrome, Firefox, Edge
2. **اختبر على أجهزة مختلفة**: Desktop, Tablet, Mobile
3. **اختبر مع بيانات مختلفة**: أصناف بأسعار مختلفة، وحدات متعددة
4. **اختبر الحالات الحدية**: أصناف بدون أسعار، أسعار صفر، أسعار كبيرة
5. **راقب الأداء**: تأكد من عدم وجود بطء في الاستجابة
6. **تحقق من التوافق**: تأكد من عدم تأثر وظائف أخرى في النظام
