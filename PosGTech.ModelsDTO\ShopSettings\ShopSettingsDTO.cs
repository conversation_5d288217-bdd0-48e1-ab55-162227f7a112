using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.ShopSettings
{
    /// <summary>
    /// نموذج بيانات إعدادات المتجر للنقل
    /// منفصل تماماً عن إعدادات المخازن
    /// </summary>
    public class ShopSettingsDTO
    {
        public Guid Id { get; set; }

        [Required(ErrorMessage = "اسم المتجر مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المتجر لا يمكن أن يتجاوز 100 حرف")]
        public string StoreName { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "عنوان المتجر لا يمكن أن يتجاوز 500 حرف")]
        public string? StoreAddress { get; set; }

        [StringLength(100, ErrorMessage = "اسم الشركة لا يمكن أن يتجاوز 100 حرف")]
        public string? CompanyName { get; set; }

        [StringLength(20, ErrorMessage = "رقم الهاتف لا يمكن أن يتجاوز 20 رقم")]
        [RegularExpression(@"^[\d\s\-\+\(\)]*$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? CompanyPhone { get; set; }

        [StringLength(500, ErrorMessage = "مسار الشعار لا يمكن أن يتجاوز 500 حرف")]
        public string? LogoPath { get; set; }

        /// <summary>
        /// يشير إلى ما إذا كانت هذه الإعدادات هي الافتراضية للنظام
        /// </summary>
        public bool IsDefault { get; set; } = false;
    }
}
