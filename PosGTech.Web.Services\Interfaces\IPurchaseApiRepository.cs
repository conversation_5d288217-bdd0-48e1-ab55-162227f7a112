using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Purchases;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IPurchaseApiRepository
    {
        /// <summary>
        /// جلب جميع المشتريات
        /// </summary>
        Task<(IEnumerable<PurchaseDTO>? list, ResponseVM? response)> GetAllPurchasesAsync();

        /// <summary>
        /// جلب جميع المشتريات للكومبو بوكس
        /// </summary>
        Task<(IEnumerable<PurchaseCMDTO>? list, ResponseVM? response)> GetAllPurchasesCMBAsync();

        /// <summary>
        /// جلب مشترى بالمعرف
        /// </summary>
        Task<(PurchaseDTO? model, ResponseVM? response)> GetPurchaseByIdAsync(Guid id);

        /// <summary>
        /// إضافة مشترى جديد
        /// </summary>
        Task<ResponseVM> InsertPurchaseAsync(PurchaseDTO purchase);

        /// <summary>
        /// تحديث مشترى موجود
        /// </summary>
        Task<ResponseVM> UpdatePurchaseAsync(Guid id, PurchaseDTO purchase);

        /// <summary>
        /// حذف مشترى
        /// </summary>
        Task<ResponseVM> DeletePurchaseAsync(Guid id);

        /// <summary>
        /// جلب أرقام المشتريات
        /// </summary>
        Task<(IEnumerable<PurchasesNumDTO>? list, ResponseVM? response)> GetPurchasesNumAsync();
    }
}
