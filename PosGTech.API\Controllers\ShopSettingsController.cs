using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.ShopSettings;

namespace PosGTech.API.Controllers
{
    /// <summary>
    /// تحكم في إعدادات المتجر - منفصل تماماً عن المخازن
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ShopSettingsController(IUnitOfWork unitOfWork, IMapper mapper) : ControllerBase
    {
        /// <summary>
        /// الحصول على جميع إعدادات المتجر
        /// </summary>
        [HttpGet("getAllShopSettings")]
        public async Task<IActionResult> GetAllShopSettings()
        {
            try
            {
                var shopSettings = mapper.Map<IEnumerable<ShopSettings>, IEnumerable<ShopSettingsDTO>>(
                    await unitOfWork.ShopSettings.GetAll());
                return Ok(shopSettings);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحميل البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// الحصول على إعدادات المتجر بالمعرف
        /// </summary>
        [HttpGet("getShopSettingsById/{id:Guid}")]
        public async Task<IActionResult> GetShopSettingsById([FromRoute] Guid id)
        {
            try
            {
                var shopSettings = await unitOfWork.ShopSettings.GetByIdAsync(id);
                if (shopSettings == null)
                    return NotFound(new ResponseVM() { Message = "إعدادات المتجر غير موجودة" });

                var result = mapper.Map<ShopSettings, ShopSettingsDTO>(shopSettings);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحميل البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// الحصول على الإعدادات الافتراضية للمتجر
        /// </summary>
        [HttpGet("getDefaultShopSettings")]
        public async Task<IActionResult> GetDefaultShopSettings()
        {
            try
            {
                var defaultSettings = await unitOfWork.ShopSettings.GetDefaultShopSettingsAsync();
                if (defaultSettings == null)
                    return NotFound(new ResponseVM() { Message = "لا توجد إعدادات افتراضية للمتجر" });

                var result = mapper.Map<ShopSettings, ShopSettingsDTO>(defaultSettings);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحميل البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// إضافة إعدادات متجر جديدة
        /// </summary>
        [HttpPost("insertShopSettings")]
        public async Task<IActionResult> InsertShopSettings([FromBody] ShopSettingsDTO model)
        {
            try
            {
                var newShopSettings = mapper.Map<ShopSettingsDTO, ShopSettings>(model);
                unitOfWork.ShopSettings.Add(newShopSettings);
                
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في حفظ البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// إضافة إعدادات متجر جديدة مع ملف الشعار
        /// </summary>
        [HttpPost("insertShopSettingsWithFile")]
        public async Task<IActionResult> InsertShopSettingsWithFile([FromForm] string? shopSettings, IFormFile? Img)
        {
            try
            {
                ShopSettingsDTO model;

                // تحليل بيانات JSON
                if (!string.IsNullOrEmpty(shopSettings))
                {
                    model = System.Text.Json.JsonSerializer.Deserialize<ShopSettingsDTO>(shopSettings);
                }
                else
                {
                    // استخدام البيانات من النموذج مباشرة
                    model = new ShopSettingsDTO
                    {
                        StoreName = Request.Form["StoreName"],
                        StoreAddress = Request.Form["StoreAddress"],
                        CompanyName = Request.Form["CompanyName"],
                        CompanyPhone = Request.Form["CompanyPhone"]
                    };
                }

                // معالجة رفع الصورة
                if (Img != null && Img.Length > 0)
                {
                    var logoPath = await SaveLogoFile(Img);
                    model.LogoPath = logoPath;
                }

                var newShopSettings = mapper.Map<ShopSettingsDTO, ShopSettings>(model);
                unitOfWork.ShopSettings.Add(newShopSettings);
                
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في حفظ البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        [HttpPut("updateShopSettings/{id:Guid}")]
        public async Task<IActionResult> UpdateShopSettings([FromRoute] Guid id, [FromBody] ShopSettingsDTO model)
        {
            try
            {
                var oldShopSettings = await unitOfWork.ShopSettings.GetByIdAsync(id);
                if (oldShopSettings == null) 
                    return BadRequest(new ResponseVM() { Message = "إعدادات المتجر غير موجودة" });

                // الاحتفاظ بالصورة القديمة إذا لم يتم تحديد صورة جديدة
                if (string.IsNullOrEmpty(model.LogoPath))
                {
                    model.LogoPath = oldShopSettings.LogoPath;
                }

                unitOfWork.ShopSettings.UpdateShopSettings(oldShopSettings, model);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحديث البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// تحديث إعدادات المتجر مع ملف الشعار
        /// </summary>
        [HttpPut("updateShopSettingsWithFile/{id:Guid}")]
        public async Task<IActionResult> UpdateShopSettingsWithFile([FromRoute] Guid id, [FromForm] string? shopSettings, IFormFile? Img)
        {
            try
            {
                var oldShopSettings = await unitOfWork.ShopSettings.GetByIdAsync(id);
                if (oldShopSettings == null) 
                    return BadRequest(new ResponseVM() { Message = "إعدادات المتجر غير موجودة" });

                ShopSettingsDTO model;

                // تحليل بيانات JSON
                if (!string.IsNullOrEmpty(shopSettings))
                {
                    model = System.Text.Json.JsonSerializer.Deserialize<ShopSettingsDTO>(shopSettings);
                }
                else
                {
                    // استخدام البيانات من النموذج مباشرة
                    model = new ShopSettingsDTO
                    {
                        StoreName = Request.Form["StoreName"],
                        StoreAddress = Request.Form["StoreAddress"],
                        CompanyName = Request.Form["CompanyName"],
                        CompanyPhone = Request.Form["CompanyPhone"]
                    };
                }

                // معالجة رفع الصورة الجديدة
                if (Img != null && Img.Length > 0)
                {
                    // حذف الصورة القديمة
                    if (!string.IsNullOrEmpty(oldShopSettings.LogoPath))
                    {
                        var oldLogoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", oldShopSettings.LogoPath.TrimStart('/'));
                        if (System.IO.File.Exists(oldLogoPath))
                        {
                            System.IO.File.Delete(oldLogoPath);
                        }
                    }

                    var logoPath = await SaveLogoFile(Img);
                    model.LogoPath = logoPath;
                }
                else
                {
                    // الاحتفاظ بالصورة القديمة إذا لم يتم رفع صورة جديدة
                    model.LogoPath = oldShopSettings.LogoPath;
                }

                unitOfWork.ShopSettings.UpdateShopSettings(oldShopSettings, model);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحديث البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// تعيين إعدادات المتجر كافتراضية
        /// </summary>
        [HttpPut("setAsDefault/{id:Guid}")]
        public async Task<IActionResult> SetAsDefault([FromRoute] Guid id)
        {
            try
            {
                var shopSettings = await unitOfWork.ShopSettings.GetByIdAsync(id);
                if (shopSettings == null) 
                    return BadRequest(new ResponseVM() { Message = "إعدادات المتجر غير موجودة" });

                await unitOfWork.ShopSettings.SetAsDefaultAsync(id);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في تحديث البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// حذف إعدادات المتجر
        /// </summary>
        [HttpDelete("deleteShopSettings/{id:Guid}")]
        public async Task<IActionResult> DeleteShopSettings([FromRoute] Guid id)
        {
            try
            {
                var shopSettings = await unitOfWork.ShopSettings.GetByIdAsync(id);
                if (shopSettings == null) 
                    return BadRequest(new ResponseVM() { Message = "إعدادات المتجر غير موجودة" });

                // منع حذف الإعدادات الافتراضية
                if (shopSettings.IsDefault)
                    return BadRequest(new ResponseVM() { Message = "لا يمكن حذف الإعدادات الافتراضية للمتجر" });

                // حذف ملف الشعار إن وجد
                if (!string.IsNullOrEmpty(shopSettings.LogoPath))
                {
                    var logoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", shopSettings.LogoPath.TrimStart('/'));
                    if (System.IO.File.Exists(logoPath))
                    {
                        System.IO.File.Delete(logoPath);
                    }
                }

                unitOfWork.ShopSettings.Remove(shopSettings);
                var res = await unitOfWork.SaveWithTransaction();
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM() { Message = "خطأ في حذف البيانات: " + ex.Message });
            }
        }

        /// <summary>
        /// حفظ ملف الشعار
        /// </summary>
        private async Task<string> SaveLogoFile(IFormFile file)
        {
            try
            {
                // التحقق من نوع الملف
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
                var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    throw new ArgumentException("نوع الملف غير مدعوم. يرجى استخدام صور بصيغة JPG, PNG, GIF أو BMP");
                }

                // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
                if (file.Length > 5 * 1024 * 1024)
                {
                    throw new ArgumentException("حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت");
                }

                // إنشاء مجلد الشعارات إذا لم يكن موجوداً
                var logoDirectory = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "logo");
                if (!Directory.Exists(logoDirectory))
                {
                    Directory.CreateDirectory(logoDirectory);
                }

                // إنشاء اسم ملف فريد
                var fileName = $"shop_logo_{Guid.NewGuid()}{fileExtension}";
                var filePath = Path.Combine(logoDirectory, fileName);

                // حفظ الملف
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // إرجاع المسار النسبي
                return $"/logo/{fileName}";
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ ملف الشعار: {ex.Message}");
            }
        }
    }
}
