using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Treasury;

namespace PosGTech.Web.Services.Interfaces
{
    public interface ITreasuryApiRepository
    {
        /// <summary>
        /// جلب جميع الخزائن
        /// </summary>
        Task<(IEnumerable<TreasuryDTO>? list, ResponseVM? response)> GetAllTreasuriesAsync();

        /// <summary>
        /// جلب جميع الخزائن للكومبو بوكس
        /// </summary>
        Task<(IEnumerable<TreasuryCMDTO>? list, ResponseVM? response)> GetAllTreasuriesCMBAsync();

        /// <summary>
        /// جلب خزينة بالمعرف
        /// </summary>
        Task<(TreasuryDTO? model, ResponseVM? response)> GetTreasuryByIdAsync(Guid id);

        /// <summary>
        /// إضافة خزينة جديدة
        /// </summary>
        Task<ResponseVM> InsertTreasuryAsync(TreasuryDTO treasury);

        /// <summary>
        /// تحديث خزينة موجودة
        /// </summary>
        Task<ResponseVM> UpdateTreasuryAsync(Guid id, TreasuryDTO treasury);

        /// <summary>
        /// حذف خزينة
        /// </summary>
        Task<ResponseVM> DeleteTreasuryAsync(Guid id);

        /// <summary>
        /// جلب خزائن المستخدم للكومبو بوكس
        /// </summary>
        Task<(IEnumerable<UserTreasuryCMDTO>? list, ResponseVM? response)> GetUserTreasuriesCMBAsync();
    }
}
