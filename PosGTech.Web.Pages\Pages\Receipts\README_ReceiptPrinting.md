# ميزة طباعة الإيصالات باستخدام Print.js

## نظرة عامة

تم تطوير ميزة طباعة الإيصالات المتقدمة في صفحة `UpsertReceipt.razor` باستخدام مكتبة Print.js مع دعم كامل للغة العربية والتصميم المتجاوب.

## الميزات الرئيسية

### 1. دعم التنسيقات المتعددة
- **طباعة حرارية (80mm)**: مناسبة للطابعات الحرارية الصغيرة
- **طباعة A5**: تنسيق متوسط للطابعات العادية
- **طباعة A4**: تنسيق كامل للطابعات المكتبية
- **طباعة تلقائية**: يتم اختيار التنسيق تلقائياً حسب حجم الشاشة

### 2. دعم اللغة العربية والـ RTL
- اتجاه النص من اليمين إلى اليسار
- خطوط عربية من Google Fonts (Noto Sans Arabic, Cairo)
- تخطيط مناسب للنصوص العربية

### 3. التصميم المتجاوب
- تكيف تلقائي مع أحجام الطابعات المختلفة
- ألوان طبيعية ومناسبة للمكاتب
- دعم الوضع الداكن والفاتح

### 4. التكامل مع إعدادات المتجر
- عرض شعار المتجر تلقائياً
- معلومات الشركة (الاسم، العنوان، الهاتف)
- تحميل الإعدادات الافتراضية من قاعدة البيانات

## الملفات المُحدثة

### 1. `PosGTech.Web/wwwroot/index.html`
```html
<!-- Print.js Library for Receipt Printing -->
<script src="https://printjs-4de6.kxcdn.com/print.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://printjs-4de6.kxcdn.com/print.min.css">

<!-- Google Fonts for Arabic Support -->
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

<script src="js/receipt-print.js"></script>
```

### 2. `PosGTech.Web/wwwroot/js/receipt-print.js`
ملف JavaScript جديد يحتوي على:
- دوال طباعة متقدمة باستخدام Print.js
- أنماط CSS متجاوبة للتنسيقات المختلفة
- دعم الوضع الداكن والفاتح
- دوال معاينة الإيصالات

### 3. `UpsertReceipt.razor`
- إضافة زر معاينة الإيصال
- قائمة منسدلة لخيارات الطباعة المختلفة
- واجهة مستخدم محسنة مع حالات التحميل

### 4. `UpsertReceipt.razor.cs`
- خدمة تحميل إعدادات المتجر الافتراضية
- دوال طباعة متقدمة مع التحقق من صحة البيانات
- إنشاء قالب HTML للإيصال
- تحويل الأرقام إلى كلمات عربية

## كيفية الاستخدام

### 1. طباعة الإيصال
1. افتح صفحة تعديل الإيصال
2. اضغط على زر "طباعة"
3. اختر نوع الطباعة المطلوب:
   - طباعة حرارية (80mm)
   - طباعة A5
   - طباعة A4
   - طباعة تلقائية

### 2. معاينة الإيصال
1. اضغط على زر "معاينة"
2. سيتم فتح نافذة جديدة تحتوي على معاينة الإيصال
3. يمكن الطباعة مباشرة من نافذة المعاينة

## التخصيص

### تخصيص الألوان
يمكن تعديل الألوان في ملف `receipt-print.js`:
```css
/* الوضع الفاتح */
.receipt-container {
    background: #ffffff;
    color: #34495e;
}

/* الوضع الداكن */
@media (prefers-color-scheme: dark) {
    .receipt-container {
        background: #2d3748;
        color: #e9ecef;
    }
}
```

### تخصيص الخطوط
يمكن تغيير الخطوط المستخدمة:
```css
body {
    font-family: 'Noto Sans Arabic', 'Cairo', Arial, sans-serif;
}
```

### إضافة حقول جديدة
لإضافة حقول جديدة للإيصال، عدّل دالة `GenerateReceiptHtml()` في `UpsertReceipt.razor.cs`.

## المتطلبات التقنية

- مكتبة Print.js (تم تحميلها من CDN)
- خطوط Google Fonts للعربية
- متصفح يدعم JavaScript ES6+
- إذن النوافذ المنبثقة للمعاينة

## استكشاف الأخطاء

### مشكلة عدم ظهور الشعار
- تأكد من رفع الشعار في إعدادات المتجر
- تحقق من صحة مسار الشعار في قاعدة البيانات

### مشكلة الطباعة
- تأكد من تفعيل JavaScript في المتصفح
- تحقق من إعدادات الطابعة
- تأكد من السماح للنوافذ المنبثقة

### مشكلة الخطوط العربية
- تأكد من الاتصال بالإنترنت لتحميل خطوط Google
- تحقق من إعدادات المتصفح للخطوط

## الدعم والتطوير

تم تطوير هذه الميزة وفقاً لأفضل الممارسات مع:
- كود نظيف ومنظم
- تعليقات شاملة باللغة العربية
- معالجة شاملة للأخطاء
- تصميم متجاوب ومتوافق مع جميع الأجهزة

للمزيد من التطوير أو الدعم، يرجى مراجعة الكود المصدري والتعليقات المرفقة.
