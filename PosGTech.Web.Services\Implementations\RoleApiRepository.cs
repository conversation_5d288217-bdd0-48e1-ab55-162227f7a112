using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Roles;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للأدوار - يحتوي على جميع العمليات المطلوبة للتعامل مع الأدوار
    /// </summary>
    public class RoleApiRepository : IRoleApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<RoleApiRepository> _logger;

        public RoleApiRepository(HttpClient httpClient, ILogger<RoleApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الأدوار
        /// </summary>
        public async Task<(IEnumerable<RoleDTO>? list, ResponseVM? response)> GetAllRolesAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الأدوار");
                var response = await _httpClient.GetAsync("Roles/getAllRoles");

                if (response.IsSuccessStatusCode)
                {
                    var roles = await response.Content.ReadFromJsonAsync<RoleDTO[]>();
                    return (roles, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأدوار");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب دور بالمعرف
        /// </summary>
        public async Task<(RoleDTO? model, ResponseVM? response)> GetRoleByIdAsync(string id)
        {
            try
            {
                _logger.LogInformation("جلب الدور بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Roles/getRoleById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var role = await response.Content.ReadFromJsonAsync<RoleDTO>();
                    return (role, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الدور بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        public async Task<ResponseVM> InsertRoleAsync(RoleDTO role)
        {
            try
            {
                _logger.LogInformation("إضافة دور جديد: {Name}", role.Name);
                var response = await _httpClient.PostAsJsonAsync("Roles/insertRole", role);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة الدور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الدور: {Name}", role.Name);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث دور موجود
        /// </summary>
        public async Task<ResponseVM> UpdateRoleAsync(string id, RoleDTO role)
        {
            try
            {
                _logger.LogInformation("تحديث الدور: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Roles/updateRole/{id}", role);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث الدور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الدور: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        public async Task<ResponseVM> DeleteRoleAsync(string id)
        {
            try
            {
                _logger.LogInformation("حذف الدور: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Roles/deleteRole/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف الدور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الدور: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب أدوار المستخدم
        /// </summary>
        public async Task<(IEnumerable<string>? roles, ResponseVM? response)> GetUserRolesAsync(string userId)
        {
            try
            {
                _logger.LogInformation("جلب أدوار المستخدم: {UserId}", userId);
                var response = await _httpClient.GetAsync($"Roles/getUserRoles/{userId}");

                if (response.IsSuccessStatusCode)
                {
                    var roles = await response.Content.ReadFromJsonAsync<string[]>();
                    return (roles, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أدوار المستخدم: {UserId}", userId);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة مستخدم إلى دور
        /// </summary>
        public async Task<ResponseVM> AddUserToRoleAsync(string userId, string roleName)
        {
            try
            {
                _logger.LogInformation("إضافة المستخدم {UserId} إلى الدور {RoleName}", userId, roleName);
                var response = await _httpClient.PostAsJsonAsync("Roles/addUserToRole", new { UserId = userId, RoleName = roleName });

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة المستخدم إلى الدور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المستخدم {UserId} إلى الدور {RoleName}", userId, roleName);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// إزالة مستخدم من دور
        /// </summary>
        public async Task<ResponseVM> RemoveUserFromRoleAsync(string userId, string roleName)
        {
            try
            {
                _logger.LogInformation("إزالة المستخدم {UserId} من الدور {RoleName}", userId, roleName);
                var response = await _httpClient.PostAsJsonAsync("Roles/removeUserFromRole", new { UserId = userId, RoleName = roleName });

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إزالة المستخدم من الدور بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إزالة المستخدم {UserId} من الدور {RoleName}", userId, roleName);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}
