using System.ComponentModel.DataAnnotations;

namespace PosGTech.ModelsDTO.Stores
{
    public class StoreSettingsDTO
    {
        public Guid Id { get; set; }
        
        [Required(ErrorMessage = "هذا الحقل مطلوب")]
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(100, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? CompanyName { get; set; }
        
        [StringLength(20, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        [RegularExpression(@"^[\d\s\-\+\(\)]*$", ErrorMessage = "رقم الهاتف غير صحيح")]
        public string? PhoneNumber { get; set; }
        
        [StringLength(500, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? Address { get; set; }
        
        [StringLength(500, ErrorMessage = "تجاوزت الحد الاعلى للحروف")]
        public string? LogoPath { get; set; }
    }
}
