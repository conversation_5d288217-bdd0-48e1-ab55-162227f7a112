using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Sells;
using PosGTech.ModelsDTO;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمبيعات - يحتوي على جميع العمليات المطلوبة للتعامل مع المبيعات
    /// </summary>
    public class SellApiRepository : ISellApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<SellApiRepository> _logger;

        public SellApiRepository(HttpClient httpClient, ILogger<SellApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع المبيعات
        /// </summary>
        public async Task<(IEnumerable<SellDTO>? list, ResponseVM? response)> GetAllSellsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المبيعات");
                var response = await _httpClient.GetAsync("Sells/getAllSells");

                if (response.IsSuccessStatusCode)
                {
                    var sells = await response.Content.ReadFromJsonAsync<SellDTO[]>();
                    return (sells, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المبيعات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع المبيعات للكومبو بوكس
        /// </summary>
        public async Task<(IEnumerable<SellCMDTO>? list, ResponseVM? response)> GetAllSellsCMBAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المبيعات للكومبو بوكس");
                var response = await _httpClient.GetAsync("Sells/getAllSellsCMB");

                if (response.IsSuccessStatusCode)
                {
                    var sells = await response.Content.ReadFromJsonAsync<SellCMDTO[]>();
                    return (sells, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المبيعات للكومبو بوكس");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب مبيعة بالمعرف
        /// </summary>
        public async Task<(SellDTO? model, ResponseVM? response)> GetSellByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب المبيعة بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Sells/getSellById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var sell = await response.Content.ReadFromJsonAsync<SellDTO>();
                    return (sell, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المبيعة بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب مبيعة برقم الفاتورة
        /// </summary>
        public async Task<(SellReceiptDTO? model, ResponseVM? response)> GetSellByNumAsync(int invoiceNo)
        {
            try
            {
                _logger.LogInformation("جلب المبيعة برقم الفاتورة: {InvoiceNo}", invoiceNo);
                var response = await _httpClient.GetAsync($"Sells/getSellByNum/{invoiceNo}");

                if (response.IsSuccessStatusCode)
                {
                    var sell = await response.Content.ReadFromJsonAsync<SellReceiptDTO>();
                    return (sell, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المبيعة برقم الفاتورة: {InvoiceNo}", invoiceNo);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة مبيعة جديدة
        /// </summary>
        public async Task<ResponseVM> InsertSellAsync(SellDTO sell)
        {
            try
            {
                _logger.LogInformation("إضافة مبيعة جديدة");
                var response = await _httpClient.PostAsJsonAsync("Sells/insertSell", sell);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة المبيعة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المبيعة");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث مبيعة موجودة
        /// </summary>
        public async Task<ResponseVM> UpdateSellAsync(Guid id, SellDTO sell)
        {
            try
            {
                _logger.LogInformation("تحديث المبيعة: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Sells/updateSell/{id}", sell);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث المبيعة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المبيعة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف مبيعة
        /// </summary>
        public async Task<ResponseVM> DeleteSellAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف المبيعة: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Sells/deleteSell/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف المبيعة بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المبيعة: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب أرقام المبيعات
        /// </summary>
        public async Task<(IEnumerable<SellsNumDTO>? list, ResponseVM? response)> GetSellsNumAsync()
        {
            try
            {
                _logger.LogInformation("جلب أرقام المبيعات");
                var response = await _httpClient.GetAsync("Sells/getSellsNum");

                if (response.IsSuccessStatusCode)
                {
                    var sellsNum = await response.Content.ReadFromJsonAsync<SellsNumDTO[]>();
                    return (sellsNum, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أرقام المبيعات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب تقرير المبيعات
        /// </summary>
        public async Task<(IEnumerable<SalesReportDTO>? list, ResponseVM? response)> GetSalesReportAsync()
        {
            try
            {
                _logger.LogInformation("جلب تقرير المبيعات");
                var response = await _httpClient.GetAsync("Sells/getSalesReport");

                if (response.IsSuccessStatusCode)
                {
                    var salesReport = await response.Content.ReadFromJsonAsync<SalesReportDTO[]>();
                    return (salesReport, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تقرير المبيعات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}
