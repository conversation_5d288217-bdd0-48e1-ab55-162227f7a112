using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PosGTech.DataAccess.Repository.IRepository;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Roles;
using System.Text.Json;

namespace PosGTech.API.Controllers
{
    /// <summary>
    /// API Controller لإدارة الأدوار والصلاحيات
    /// يتبع النمط المعياري المستخدم في باقي المشروع
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RolesController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public RolesController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// الحصول على جميع الأدوار مع صلاحياتها
        /// </summary>
        [HttpGet("getAllRoles")]
        public async Task<IActionResult> GetAllRoles()
        {
            var roles = await _unitOfWork.Role.GetAllRoles();
            return Ok(roles);
        }

        /// <summary>
        /// الحصول على دور محدد بمعرفه
        /// </summary>
        [HttpGet("getRoleById/{id}")]
        public async Task<IActionResult> GetRoleById([FromRoute] Guid id)
        {
            var role = await _unitOfWork.Role.GetRoleById(id);
            if (role == null)
                return BadRequest(new ResponseVM { State = false, Message = "البيانات غير موجودة" });
            return Ok(role);
        }

        /// <summary>
        /// إنشاء دور جديد مع صلاحياته
        /// </summary>
        [HttpPost("insertRole")]
        public async Task<IActionResult> InsertRole([FromBody] RoleDTO model)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new ResponseVM { State = false, Message = "الرجاء تعبئة البيانات بشكل صحيح" });

                if (model == null)
                    return BadRequest(new ResponseVM { State = false, Message = "بيانات الدور مطلوبة" });

                var res = await _unitOfWork.Role.InsertRole(model);
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM { State = false, Message = $"خطأ في إنشاء الدور: {ex.Message}" });
            }
        }

        /// <summary>
        /// تحديث دور موجود
        /// </summary>
        [HttpPut("updateRole/{id}")]
        public async Task<IActionResult> UpdateRole([FromRoute] Guid id, [FromBody] RoleDTO model)
        {
            try
            {
                // تسجيل البيانات المستلمة للتشخيص
                Console.WriteLine($"UpdateRole called with ID: {id}");
                Console.WriteLine($"Model received: {model != null}");
                if (model != null)
                {
                    Console.WriteLine($"Model Name: {model.Name}");
                    Console.WriteLine($"Model Permissions Count: {model.Permissions?.Count ?? 0}");
                }

                if (!ModelState.IsValid)
                {
                    var errors = string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage));
                    return BadRequest(new ResponseVM { State = false, Message = $"الرجاء تعبئة البيانات بشكل صحيح: {errors}" });
                }

                if (id == Guid.Empty)
                    return BadRequest(new ResponseVM { State = false, Message = "معرف الدور مطلوب" });

                if (model == null)
                    return BadRequest(new ResponseVM { State = false, Message = "بيانات الدور مطلوبة" });

                var res = await _unitOfWork.Role.UpdateRole(id, model);
                if (res.State) return Ok(res);
                else return BadRequest(res);
            }
            catch (JsonException jsonEx)
            {
                return BadRequest(new ResponseVM { State = false, Message = $"خطأ في تحليل البيانات: {jsonEx.Message}" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseVM { State = false, Message = $"خطأ في تحديث الدور: {ex.Message}" });
            }
        }

        /// <summary>
        /// حذف دور
        /// </summary>
        [HttpDelete("deleteRole/{id}")]
        public async Task<IActionResult> DeleteRole([FromRoute] Guid id)
        {
            var res = await _unitOfWork.Role.DeleteRole(id);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        /// <summary>
        /// الحصول على صلاحيات دور محدد
        /// </summary>
        [HttpGet("getRolePermissions/{id}")]
        public async Task<IActionResult> GetRolePermissions([FromRoute] Guid id)
        {
            var permissions = await _unitOfWork.Role.GetRolePermissions(id);
            return Ok(permissions);
        }

        /// <summary>
        /// تحديث صلاحيات دور محدد
        /// </summary>
        [HttpPost("updateRolePermissions")]
        public async Task<IActionResult> UpdateRolePermissions([FromBody] UpdateRolePermissionsDTO model)
        {
            var res = await _unitOfWork.Role.UpdateRolePermissions(model.RoleId, model.Permissions);
            if (res.State) return Ok(res);
            else return BadRequest(res);
        }

        /// <summary>
        /// الحصول على المستخدمين المخصصين لدور محدد
        /// </summary>
        [HttpGet("getRoleUsers/{id}")]
        public async Task<IActionResult> GetRoleUsers([FromRoute] Guid id)
        {
            var roleUsers = await _unitOfWork.Role.GetRoleUsers(id);
            return Ok(roleUsers);
        }

        /// <summary>
        /// الحصول على تفاصيل الدور مع المستخدمين
        /// </summary>
        [HttpGet("getRoleDetails/{id}")]
        public async Task<IActionResult> GetRoleDetails([FromRoute] Guid id)
        {
            var roleDetails = await _unitOfWork.Role.GetRoleDetails(id);
            if (roleDetails == null)
                return BadRequest(new ResponseVM { State = false, Message = "البيانات غير موجودة" });
            return Ok(roleDetails);
        }

        /// <summary>
        /// الحصول على إحصائيات الأدوار
        /// </summary>
        [HttpGet("getRoleStatistics")]
        public async Task<IActionResult> GetRoleStatistics()
        {
            var statistics = await _unitOfWork.Role.GetRoleStatistics();
            return Ok(statistics);
        }
    }
}
