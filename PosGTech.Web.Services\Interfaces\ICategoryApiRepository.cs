using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Categories;

namespace PosGTech.Web.Services.Interfaces
{
    public interface ICategoryApiRepository
    {
        /// <summary>
        /// جلب جميع التصنيفات
        /// </summary>
        Task<(IEnumerable<CategoryDTO>? list, ResponseVM? response)> GetAllCategoriesAsync();

        /// <summary>
        /// جلب تصنيف بالمعرف
        /// </summary>
        Task<(CategoryDTO? model, ResponseVM? response)> GetCategoryByIdAsync(Guid id);

        /// <summary>
        /// إضافة تصنيف جديد
        /// </summary>
        Task<ResponseVM> InsertCategoryAsync(CategoryDTO category);

        /// <summary>
        /// تحديث تصنيف موجود
        /// </summary>
        Task<ResponseVM> UpdateCategoryAsync(Guid id, CategoryDTO category);

        /// <summary>
        /// حذف تصنيف
        /// </summary>
        Task<ResponseVM> DeleteCategoryAsync(Guid id);
    }
}
