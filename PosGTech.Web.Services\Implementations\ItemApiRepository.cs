using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Items;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للأصناف - يحتوي على جميع العمليات المطلوبة للتعامل مع الأصناف
    /// </summary>
    public class ItemApiRepository : IItemApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ItemApiRepository> _logger;

        public ItemApiRepository(HttpClient httpClient, ILogger<ItemApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }



        /// <summary>
        /// جلب جميع الأصناف مع التفاصيل الكاملة
        /// </summary>
        public async Task<(IEnumerable<ItemDTO>? list, ResponseVM? response)> GetAllItemsAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب جميع الأصناف");

                var httpResponse = await _httpClient.GetAsync("Items/getAllItems");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var items = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<ItemDTO>>();
                    _logger.LogInformation("تم جلب {Count} صنف بنجاح", items?.Count() ?? 0);
                    return (items, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الأصناف: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الأصناف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الأصناف");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب جميع الأصناف للمشتريات
        /// </summary>
        public async Task<(IEnumerable<ItemDTO>? list, ResponseVM? response)> GetAllItemsForPurchaseAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب الأصناف للمشتريات");

                var httpResponse = await _httpClient.GetAsync("Items/getAllItemsForPurchase");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var items = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<ItemDTO>>();
                    _logger.LogInformation("تم جلب {Count} صنف للمشتريات بنجاح", items?.Count() ?? 0);
                    return (items, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الأصناف للمشتريات: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الأصناف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الأصناف للمشتريات");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب جميع الأصناف للقوائم المنسدلة
        /// </summary>
        public async Task<(IEnumerable<ItemCMDTO>? list, ResponseVM? response)> GetAllItemsCMAsync()
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب الأصناف للقوائم المنسدلة");

                var httpResponse = await _httpClient.GetAsync("Items/getAllItemsCM");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var items = await httpResponse.Content.ReadFromJsonAsync<IEnumerable<ItemCMDTO>>();
                    _logger.LogInformation("تم جلب {Count} صنف للقوائم المنسدلة بنجاح", items?.Count() ?? 0);
                    return (items, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الأصناف للقوائم المنسدلة: {StatusCode} - {Error}", httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الأصناف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الأصناف للقوائم المنسدلة");
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// جلب صنف بالمعرف
        /// </summary>
        public async Task<(ItemDTO? model, ResponseVM? response)> GetItemByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية جلب الصنف بالمعرف: {ItemId}", id);

                var httpResponse = await _httpClient.GetAsync($"Items/getItemById/{id}");

                if (httpResponse.IsSuccessStatusCode)
                {
                    var item = await httpResponse.Content.ReadFromJsonAsync<ItemDTO>();
                    _logger.LogInformation("تم جلب الصنف بالمعرف {ItemId} بنجاح", id);
                    return (item, null);
                }
                else
                {
                    var errorContent = await httpResponse.Content.ReadAsStringAsync();
                    _logger.LogWarning("فشل في جلب الصنف بالمعرف {ItemId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorContent);
                    return (null, new ResponseVM { Message = $"خطأ في جلب الصنف: {httpResponse.StatusCode}", State = false });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء جلب الصنف بالمعرف: {ItemId}", id);
                return (null, new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false });
            }
        }

        /// <summary>
        /// إضافة صنف جديد
        /// </summary>
        public async Task<ResponseVM> InsertItemAsync(ItemDTO item)
        {
            try
            {
                _logger.LogInformation("بدء عملية إضافة صنف جديد: {ItemName}", item.Name);

                var json = JsonSerializer.Serialize(item);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PostAsync("Items/insertItem", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم إضافة الصنف {ItemName} بنجاح", item.Name);
                    return response ?? new ResponseVM { Message = "تم إضافة الصنف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في إضافة الصنف {ItemName}: {StatusCode} - {Error}", item.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في إضافة الصنف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء إضافة الصنف: {ItemName}", item.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// تحديث صنف موجود
        /// </summary>
        public async Task<ResponseVM> UpdateItemAsync(Guid id, ItemDTO item)
        {
            try
            {
                _logger.LogInformation("بدء عملية تحديث الصنف: {ItemId} - {ItemName}", id, item.Name);

                var json = JsonSerializer.Serialize(item);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var httpResponse = await _httpClient.PutAsync($"Items/updateItem/{id}", content);
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم تحديث الصنف {ItemId} - {ItemName} بنجاح", id, item.Name);
                    return response ?? new ResponseVM { Message = "تم تحديث الصنف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في تحديث الصنف {ItemId} - {ItemName}: {StatusCode} - {Error}", id, item.Name, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في تحديث الصنف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء تحديث الصنف: {ItemId} - {ItemName}", id, item.Name);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }

        /// <summary>
        /// حذف صنف
        /// </summary>
        public async Task<ResponseVM> DeleteItemAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("بدء عملية حذف الصنف: {ItemId}", id);

                var httpResponse = await _httpClient.DeleteAsync($"Items/deleteItem/{id}");
                var responseContent = await httpResponse.Content.ReadAsStringAsync();

                if (httpResponse.IsSuccessStatusCode)
                {
                    var response = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogInformation("تم حذف الصنف {ItemId} بنجاح", id);
                    return response ?? new ResponseVM { Message = "تم حذف الصنف بنجاح", State = true };
                }
                else
                {
                    var errorResponse = JsonSerializer.Deserialize<ResponseVM>(responseContent);
                    _logger.LogWarning("فشل في حذف الصنف {ItemId}: {StatusCode} - {Error}", id, httpResponse.StatusCode, errorResponse?.Message);
                    return errorResponse ?? new ResponseVM { Message = "خطأ في حذف الصنف", State = false };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ غير متوقع أثناء حذف الصنف: {ItemId}", id);
                return new ResponseVM { Message = "خطأ في الاتصال بالخادم", State = false };
            }
        }
    }
}
