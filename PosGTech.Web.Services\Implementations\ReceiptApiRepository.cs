using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Receipts;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للإيصالات - يحتوي على جميع العمليات المطلوبة للتعامل مع الإيصالات
    /// </summary>
    public class ReceiptApiRepository : IReceiptApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ReceiptApiRepository> _logger;

        public ReceiptApiRepository(HttpClient httpClient, ILogger<ReceiptApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع الإيصالات
        /// </summary>
        public async Task<(IEnumerable<ReceiptDTO>? list, ResponseVM? response)> GetAllReceiptsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع الإيصالات");
                var response = await _httpClient.GetAsync("Receipts/getAllReceipts");

                if (response.IsSuccessStatusCode)
                {
                    var receipts = await response.Content.ReadFromJsonAsync<ReceiptDTO[]>();
                    return (receipts, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإيصالات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب إيصال بالمعرف
        /// </summary>
        public async Task<(ReceiptDTO? model, ResponseVM? response)> GetReceiptByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب الإيصال بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Receipts/getReceiptById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var receipt = await response.Content.ReadFromJsonAsync<ReceiptDTO>();
                    return (receipt, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإيصال بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة إيصال جديد
        /// </summary>
        public async Task<ResponseVM> InsertReceiptAsync(ReceiptDTO receipt)
        {
            try
            {
                _logger.LogInformation("إضافة إيصال جديد");
                var response = await _httpClient.PostAsJsonAsync("Receipts/insertReceipt", receipt);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة الإيصال بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة الإيصال");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث إيصال موجود
        /// </summary>
        public async Task<ResponseVM> UpdateReceiptAsync(Guid id, ReceiptDTO receipt)
        {
            try
            {
                _logger.LogInformation("تحديث الإيصال: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Receipts/updateReceipt/{id}", receipt);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث الإيصال بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الإيصال: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف إيصال
        /// </summary>
        public async Task<ResponseVM> DeleteReceiptAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف الإيصال: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Receipts/deleteReceipt/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف الإيصال بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الإيصال: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }
    }
}
