using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Roles;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IRoleApiRepository
    {
        /// <summary>
        /// جلب جميع الأدوار
        /// </summary>
        Task<(IEnumerable<RoleDTO>? list, ResponseVM? response)> GetAllRolesAsync();

        /// <summary>
        /// جلب دور بالمعرف
        /// </summary>
        Task<(RoleDTO? model, ResponseVM? response)> GetRoleByIdAsync(string id);

        /// <summary>
        /// إضافة دور جديد
        /// </summary>
        Task<ResponseVM> InsertRoleAsync(RoleDTO role);

        /// <summary>
        /// تحديث دور موجود
        /// </summary>
        Task<ResponseVM> UpdateRoleAsync(string id, RoleDTO role);

        /// <summary>
        /// حذف دور
        /// </summary>
        Task<ResponseVM> DeleteRoleAsync(string id);

        /// <summary>
        /// جلب أدوار المستخدم
        /// </summary>
        Task<(IEnumerable<string>? roles, ResponseVM? response)> GetUserRolesAsync(string userId);

        /// <summary>
        /// إضافة مستخدم إلى دور
        /// </summary>
        Task<ResponseVM> AddUserToRoleAsync(string userId, string roleName);

        /// <summary>
        /// إزالة مستخدم من دور
        /// </summary>
        Task<ResponseVM> RemoveUserFromRoleAsync(string userId, string roleName);
    }
}
