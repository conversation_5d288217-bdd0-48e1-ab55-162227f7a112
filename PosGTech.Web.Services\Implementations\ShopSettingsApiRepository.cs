using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.ShopSettings;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API لإعدادات المتجر - يحتوي على جميع العمليات المطلوبة للتعامل مع إعدادات المتجر
    /// </summary>
    public class ShopSettingsApiRepository : IShopSettingsApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<ShopSettingsApiRepository> _logger;

        public ShopSettingsApiRepository(HttpClient httpClient, ILogger<ShopSettingsApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع إعدادات المتجر
        /// </summary>
        public async Task<(IEnumerable<ShopSettingsDTO>? list, ResponseVM? response)> GetAllShopSettingsAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع إعدادات المتجر");
                var response = await _httpClient.GetAsync("ShopSettings/getAllShopSettings");

                if (response.IsSuccessStatusCode)
                {
                    var shopSettingsList = await response.Content.ReadFromJsonAsync<IEnumerable<ShopSettingsDTO>>();
                    return (shopSettingsList, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع إعدادات المتجر");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب إعدادات المتجر بالمعرف
        /// </summary>
        public async Task<(ShopSettingsDTO? model, ResponseVM? response)> GetShopSettingsByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب إعدادات المتجر بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"ShopSettings/getShopSettingsById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var shopSettings = await response.Content.ReadFromJsonAsync<ShopSettingsDTO>();
                    return (shopSettings, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات المتجر بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب إعدادات المتجر
        /// </summary>
        public async Task<(ShopSettingsDTO? model, ResponseVM? response)> GetShopSettingsAsync()
        {
            try
            {
                _logger.LogInformation("جلب إعدادات المتجر");
                var response = await _httpClient.GetAsync("ShopSettings/getShopSettings");

                if (response.IsSuccessStatusCode)
                {
                    var shopSettings = await response.Content.ReadFromJsonAsync<ShopSettingsDTO>();
                    return (shopSettings, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات المتجر");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إدراج إعدادات المتجر
        /// </summary>
        public async Task<ResponseVM> InsertShopSettingsAsync(ShopSettingsDTO shopSettings)
        {
            try
            {
                _logger.LogInformation("إدراج إعدادات المتجر جديدة");
                var response = await _httpClient.PostAsJsonAsync("ShopSettings/insertShopSettings", shopSettings);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إدراج إعدادات المتجر بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إدراج إعدادات المتجر");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        public async Task<ResponseVM> UpdateShopSettingsAsync(Guid id, ShopSettingsDTO shopSettings)
        {
            try
            {
                _logger.LogInformation("تحديث إعدادات المتجر: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"ShopSettings/updateShopSettings/{id}", shopSettings);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث إعدادات المتجر بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات المتجر: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف إعدادات المتجر
        /// </summary>
        public async Task<ResponseVM> DeleteShopSettingsAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف إعدادات المتجر: {Id}", id);
                var response = await _httpClient.DeleteAsync($"ShopSettings/deleteShopSettings/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف إعدادات المتجر بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف إعدادات المتجر: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// إدراج إعدادات المتجر مع ملف
        /// </summary>
        public async Task<ResponseVM> InsertShopSettingsWithFileAsync(ShopSettingsDTO shopSettings, IBrowserFile file)
        {
            try
            {
                _logger.LogInformation("إدراج إعدادات المتجر مع ملف");

                using var content = new MultipartFormDataContent();
                content.Add(new StringContent(JsonSerializer.Serialize(shopSettings), Encoding.UTF8, "application/json"), "shopSettings");

                var fileContent = new StreamContent(file.OpenReadStream());
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType);
                content.Add(fileContent, "file", file.Name);

                var response = await _httpClient.PostAsync("ShopSettings/insertShopSettingsWithFile", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إدراج إعدادات المتجر مع الملف بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إدراج إعدادات المتجر مع ملف");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث إعدادات المتجر مع ملف
        /// </summary>
        public async Task<ResponseVM> UpdateShopSettingsWithFileAsync(Guid id, ShopSettingsDTO shopSettings, IBrowserFile file)
        {
            try
            {
                _logger.LogInformation("تحديث إعدادات المتجر مع ملف: {Id}", id);

                using var content = new MultipartFormDataContent();
                content.Add(new StringContent(JsonSerializer.Serialize(shopSettings), Encoding.UTF8, "application/json"), "shopSettings");

                var fileContent = new StreamContent(file.OpenReadStream());
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType);
                content.Add(fileContent, "file", file.Name);

                var response = await _httpClient.PutAsync($"ShopSettings/updateShopSettingsWithFile/{id}", content);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث إعدادات المتجر مع الملف بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات المتجر مع ملف: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        public async Task<ResponseVM> UpdateShopSettingsAsync(ShopSettingsDTO shopSettings)
        {
            try
            {
                _logger.LogInformation("تحديث إعدادات المتجر");
                var response = await _httpClient.PutAsJsonAsync("ShopSettings/updateShopSettings", shopSettings);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث إعدادات المتجر بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعدادات المتجر");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// إعادة تعيين إعدادات المتجر للقيم الافتراضية
        /// </summary>
        public async Task<ResponseVM> ResetShopSettingsAsync()
        {
            try
            {
                _logger.LogInformation("إعادة تعيين إعدادات المتجر للقيم الافتراضية");
                var response = await _httpClient.PostAsync("ShopSettings/resetShopSettings", null);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إعادة تعيين إعدادات المتجر بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إعادة تعيين إعدادات المتجر");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب إعدادات المتجر الافتراضية
        /// </summary>
        public async Task<(ShopSettingsDTO? model, ResponseVM? response)> GetDefaultShopSettingsAsync()
        {
            try
            {
                _logger.LogInformation("جلب إعدادات المتجر الافتراضية");
                var response = await _httpClient.GetAsync("ShopSettings/getDefaultShopSettings");

                if (response.IsSuccessStatusCode)
                {
                    var shopSettings = await response.Content.ReadFromJsonAsync<ShopSettingsDTO>();
                    return (shopSettings, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات المتجر الافتراضية");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}
