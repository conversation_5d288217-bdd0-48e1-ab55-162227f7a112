using Microsoft.Extensions.Logging;
using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Purchases;
using PosGTech.Web.Services.Interfaces;
using System.Net.Http.Json;

namespace PosGTech.Web.Services.Implementations
{
    /// <summary>
    /// تنفيذ مستودع API للمشتريات - يحتوي على جميع العمليات المطلوبة للتعامل مع المشتريات
    /// </summary>
    public class PurchaseApiRepository : IPurchaseApiRepository
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<PurchaseApiRepository> _logger;

        public PurchaseApiRepository(HttpClient httpClient, ILogger<PurchaseApiRepository> logger)
        {
            _httpClient = httpClient;
            _logger = logger;
        }

        /// <summary>
        /// جلب جميع المشتريات
        /// </summary>
        public async Task<(IEnumerable<PurchaseDTO>? list, ResponseVM? response)> GetAllPurchasesAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المشتريات");
                var response = await _httpClient.GetAsync("Purchases/getAllPurchases");

                if (response.IsSuccessStatusCode)
                {
                    var purchases = await response.Content.ReadFromJsonAsync<PurchaseDTO[]>();
                    return (purchases, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المشتريات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب جميع المشتريات للكومبو بوكس
        /// </summary>
        public async Task<(IEnumerable<PurchaseCMDTO>? list, ResponseVM? response)> GetAllPurchasesCMBAsync()
        {
            try
            {
                _logger.LogInformation("جلب جميع المشتريات للكومبو بوكس");
                var response = await _httpClient.GetAsync("Purchases/getAllPurchasesCMB");

                if (response.IsSuccessStatusCode)
                {
                    var purchases = await response.Content.ReadFromJsonAsync<PurchaseCMDTO[]>();
                    return (purchases, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المشتريات للكومبو بوكس");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// جلب مشترى بالمعرف
        /// </summary>
        public async Task<(PurchaseDTO? model, ResponseVM? response)> GetPurchaseByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("جلب المشترى بالمعرف: {Id}", id);
                var response = await _httpClient.GetAsync($"Purchases/getPurchaseById/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var purchase = await response.Content.ReadFromJsonAsync<PurchaseDTO>();
                    return (purchase, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب المشترى بالمعرف: {Id}", id);
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }

        /// <summary>
        /// إضافة مشترى جديد
        /// </summary>
        public async Task<ResponseVM> InsertPurchaseAsync(PurchaseDTO purchase)
        {
            try
            {
                _logger.LogInformation("إضافة مشترى جديد");
                var response = await _httpClient.PostAsJsonAsync("Purchases/insertPurchase", purchase);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم إضافة المشترى بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة المشترى");
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// تحديث مشترى موجود
        /// </summary>
        public async Task<ResponseVM> UpdatePurchaseAsync(Guid id, PurchaseDTO purchase)
        {
            try
            {
                _logger.LogInformation("تحديث المشترى: {Id}", id);
                var response = await _httpClient.PutAsJsonAsync($"Purchases/updatePurchase/{id}", purchase);

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم تحديث المشترى بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث المشترى: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// حذف مشترى
        /// </summary>
        public async Task<ResponseVM> DeletePurchaseAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("حذف المشترى: {Id}", id);
                var response = await _httpClient.DeleteAsync($"Purchases/deletePurchase/{id}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<ResponseVM>();
                    return result ?? new ResponseVM { State = true, Message = "تم حذف المشترى بنجاح" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return new ResponseVM { State = false, Message = errorContent };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف المشترى: {Id}", id);
                return new ResponseVM { State = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// جلب أرقام المشتريات
        /// </summary>
        public async Task<(IEnumerable<PurchasesNumDTO>? list, ResponseVM? response)> GetPurchasesNumAsync()
        {
            try
            {
                _logger.LogInformation("جلب أرقام المشتريات");
                var response = await _httpClient.GetAsync("Purchases/getPurchasesNum");

                if (response.IsSuccessStatusCode)
                {
                    var purchasesNum = await response.Content.ReadFromJsonAsync<PurchasesNumDTO[]>();
                    return (purchasesNum, null);
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    return (null, new ResponseVM { State = false, Message = errorContent });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أرقام المشتريات");
                return (null, new ResponseVM { State = false, Message = ex.Message });
            }
        }
    }
}
