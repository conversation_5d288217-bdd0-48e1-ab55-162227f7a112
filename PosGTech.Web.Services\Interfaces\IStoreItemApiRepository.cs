using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.StoreItem;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IStoreItemApiRepository
    {
        /// <summary>
        /// جلب جميع عناصر المخزن
        /// </summary>
        Task<(IEnumerable<StoreItemDTO>? list, ResponseVM? response)> GetAllStoreItemsAsync();

        /// <summary>
        /// جلب عناصر المخزن للبيع
        /// </summary>
        Task<(IEnumerable<StoreItemForSellDTO>? list, ResponseVM? response)> GetStoreItemsForSellAsync();

        /// <summary>
        /// جلب عناصر المخزن للبيع حسب المخزن
        /// </summary>
        Task<(IEnumerable<StoreItemDTO>? list, ResponseVM? response)> GetStoreItemsForSellAsync(Guid storeId);

        /// <summary>
        /// جلب عنصر مخزن بالمعرف
        /// </summary>
        Task<(StoreItemDTO? model, ResponseVM? response)> GetStoreItemByIdAsync(Guid id);

        /// <summary>
        /// إضافة عنصر مخزن جديد
        /// </summary>
        Task<ResponseVM> InsertStoreItemAsync(StoreItemDTO storeItem);

        /// <summary>
        /// تحديث عنصر مخزن موجود
        /// </summary>
        Task<ResponseVM> UpdateStoreItemAsync(Guid id, StoreItemDTO storeItem);

        /// <summary>
        /// حذف عنصر مخزن
        /// </summary>
        Task<ResponseVM> DeleteStoreItemAsync(Guid id);
    }
}
