using PosGTech.Models;
using PosGTech.ModelsDTO.ShopSettings;

namespace PosGTech.DataAccess.Repository.IRepository
{
    /// <summary>
    /// واجهة مستودع إعدادات المتجر
    /// منفصلة تماماً عن مستودع المخازن
    /// </summary>
    public interface IShopSettingsRepository : IRepository<ShopSettings>
    {
        /// <summary>
        /// تحديث إعدادات المتجر
        /// </summary>
        /// <param name="oldShopSettings">الإعدادات القديمة</param>
        /// <param name="newShopSettings">الإعدادات الجديدة</param>
        void UpdateShopSettings(ShopSettings oldShopSettings, ShopSettingsDTO newShopSettings);

        /// <summary>
        /// الحصول على الإعدادات الافتراضية للمتجر
        /// </summary>
        /// <returns>إعدادات المتجر الافتراضية</returns>
        Task<ShopSettings?> GetDefaultShopSettingsAsync();

        /// <summary>
        /// تعيين إعدادات المتجر كافتراضية
        /// </summary>
        /// <param name="shopSettingsId">معرف إعدادات المتجر</param>
        Task SetAsDefaultAsync(Guid shopSettingsId);
    }
}
