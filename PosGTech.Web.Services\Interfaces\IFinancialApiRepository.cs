using PosGTech.Models.ViewModels;
using PosGTech.ModelsDTO.Finacnial;

namespace PosGTech.Web.Services.Interfaces
{
    public interface IFinancialApiRepository
    {
        /// <summary>
        /// جلب جميع العمليات المالية
        /// </summary>
        Task<(IEnumerable<FinancialDTO>? list, ResponseVM? response)> GetAllFinancialsAsync();

        /// <summary>
        /// جلب عملية مالية بالمعرف
        /// </summary>
        Task<(FinancialDTO? model, ResponseVM? response)> GetFinancialByIdAsync(Guid id);

        /// <summary>
        /// إضافة عملية مالية جديدة
        /// </summary>
        Task<ResponseVM> InsertFinancialAsync(FinancialDTO financial);

        /// <summary>
        /// تحديث عملية مالية موجودة
        /// </summary>
        Task<ResponseVM> UpdateFinancialAsync(Guid id, FinancialDTO financial);

        /// <summary>
        /// حذف عملية مالية
        /// </summary>
        Task<ResponseVM> DeleteFinancialAsync(Guid id);
    }
}
