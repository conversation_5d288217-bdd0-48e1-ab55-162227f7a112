@page "/listRoles"
@using PosGTech.ModelsDTO.Roles
@using PosGTech.ModelsDTO.Authorization
@attribute [Authorize(Policy = "permission")]

<PageTitle>إدارة الأدوار</PageTitle>

<AuthorizeView Policy="@PermissionConstants.RolesView" Context="auth">
    <Authorized>
        <MudPaper Class="d-flex flex-column desktop rounded-lg" Elevation="0">
    <MudTable Items="@roles" 
              Height="calc(100vh - 170px)" 
              Loading="loading" 
              Breakpoint="Breakpoint.Sm" 
              Filter="new Func<RoleDTO,bool>(FilterFunc1)" 
              @bind-SelectedItem="selectedItem" 
              Virtualize="true" 
              Striped="true" 
              Dense="true" 
              FixedHeader="true" 
              Elevation="4">

        <ToolBarContent>
            <MudText Align="@Align.Justify" Typo="Typo.h6" Style="margin-top:24px; margin-left:48px;">الأدوار</MudText>
            <MudTextField @bind-Value="Search" Placeholder="بحث" Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
            <MudSpacer />
            <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.Add" OnClick="()=>NavigateToUpsertRole(Guid.Empty)" Color="Color.Primary">جديد</MudButton>
        </ToolBarContent>

        <HeaderContent>
            <MudTh Class="rounded-0">ت</MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<RoleDTO, object>(x=>x.Name)">اسم الدور</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<RoleDTO, object>(x=>x.Permissions.Count)">عدد الصلاحيات</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<RoleDTO, object>(x=>x.UsersCount)">عدد المستخدمين</MudTableSortLabel></MudTh>
            <MudTh><MudTableSortLabel SortBy="new Func<RoleDTO, object>(x=>x.IsSystemRole)">النوع</MudTableSortLabel></MudTh>
            <MudTh Class="rounded-0">الإجراءات</MudTh>
        </HeaderContent>

        <RowTemplate>
            <MudTd DataLabel="ت">@(roles.ToList().IndexOf(context) + 1)</MudTd>
            <MudTd DataLabel="اسم الدور">
                <div class="d-flex align-center">
                    <MudIcon Icon="@GetRoleIcon(context.Name)" Class="ml-2" Color="@GetRoleColor(context)" />
                    <MudText Typo="Typo.body1" Class="font-weight-medium">@context.Name</MudText>
                </div>
            </MudTd>
            <MudTd DataLabel="عدد الصلاحيات">
                <MudChip T="string" Size="Size.Small" Color="Color.Primary">
                    @context.Permissions.Count صلاحية
                </MudChip>
            </MudTd>
            <MudTd DataLabel="عدد المستخدمين">
                <MudChip T="string" Size="Size.Small" Color="@(context.UsersCount > 0 ? Color.Success : Color.Default)">
                    @context.UsersCount مستخدم
                </MudChip>
            </MudTd>
            <MudTd DataLabel="النوع">
                @if (context.IsSystemRole)
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Success">دور أساسي</MudChip>
                }
                else
                {
                    <MudChip T="string" Size="Size.Small" Color="Color.Info">دور مخصص</MudChip>
                }
            </MudTd>
            <MudTd DataLabel="الإجراءات">
                <MudButtonGroup Variant="Variant.Text" Size="Size.Small">
                    <MudIconButton Icon="@Icons.Material.Filled.Edit" 
                                   Color="Color.Primary" 
                                   Title="تعديل"
                                   OnClick="@(() => NavigateToUpsertRole(context.Id.Value))" />
                    
                    @if (!context.IsSystemRole)
                    {
                        <MudIconButton Icon="@Icons.Material.Filled.Delete" 
                                       Color="Color.Error" 
                                       Title="حذف"
                                       Disabled="@(context.UsersCount > 0)"
                                       OnClick="@(() => DeleteRole(context))" />
                    }
                </MudButtonGroup>
            </MudTd>
        </RowTemplate>

        <PagerContent>
            <MudTablePager style="height:24px;" PageSizeOptions="new int[] {int.MaxValue }"
                           RowsPerPageString="عدد الصفوف :"
                           InfoFormat="{last_item} -- {all_items}"
                           HorizontalAlignment="HorizontalAlignment.Center"
                           HideRowsPerPage="true"
                           HidePageNumber="true"
                           HidePagination="true" />
        </PagerContent>
    </MudTable>
</MudPaper>

<MudMessageBox @ref="mbox" Title="تنبيه" CancelText="إلغاء">
    <MessageContent>
        هل تريد حذف الدور <b>@NameRoleForDelete</b>؟
    </MessageContent>
    <YesButton>
        <MudButton Variant="Variant.Filled" Color="Color.Error" StartIcon="@Icons.Material.Filled.Delete">حذف</MudButton>
    </YesButton>
</MudMessageBox>
    </Authorized>
    <NotAuthorized>
        <MudAlert Severity="Severity.Error" Variant="Variant.Filled" Class="ma-4">
            ليس لديك صلاحية الوصول لعرض الأدوار
        </MudAlert>
    </NotAuthorized>
</AuthorizeView>
